/**
 * @create 王家麒@2023.02.03
 */
const axios = require('axios');
const urlParse = require('url-parse');
const { time: { currentTimestamp } } = require('@baidu/bat-util');
const { URLSearchParams } = require('url');
const { logger } = require('./logger-worker');
const { handleUuapCookie } = require('./cookie');
const { isEmpty } = require('./param');

const _checkRequestPath = async (path, query) => {
    isEmpty({ path });
    let urlObj = urlParse(path, {}, true);
    let newQuery = Object.assign(query, urlObj.query);
    return urlObj.set('query', newQuery).href;
};

const _checkRequestHeader = async (body, headers) => {
    if (!headers['Content-Type']) {
        headers['Content-Type'] = 'application/json';
    }
    if ('application/x-www-form-urlencoded' === headers['Content-Type']) {
        let newBody = new URLSearchParams();
        for (let key of Object.keys(body)) {
            newBody.append(key, body[key]);
        }
        body = newBody;
    }
    return { body, headers };
};

const _get = async (axiosItem, baseURL, path, body, headers, timeout) => {
    let { data } = await axiosItem.get(
        path,
        {
            data: body,
            baseURL,
            withCredentials: true,
            headers,
            timeout
        }
    );
    return data;
};

const _post = async (axiosItem, baseURL, path, body, headers, timeout) => {
    let { data } = await axiosItem.post(
        path,
        body,
        {
            baseURL,
            withCredentials: true,
            headers,
            timeout
        }
    );
    return data;
};

const _put = async (axiosItem, baseURL, path, body, headers, timeout) => {
    let { data } = await axiosItem.put(
        path,
        body,
        {
            baseURL,
            withCredentials: true,
            headers,
            timeout
        }
    );
    return data;
};

const _del = async (axiosItem, baseURL, path, body, headers, timeout) => {
    let { data } = await axiosItem.delete(
        path,
        {
            data: body,
            baseURL,
            withCredentials: true,
            headers,
            timeout
        }
    );
    return data;
};

const cloudServerRequest = async (
    {
        baseURL = '',
        path = '',
        query = {},
        body = {},
        method = 'GET',
        cookie = '',
        headers = { 'Content-Type': 'application/json', 'Accept-Encoding': 'gzip' },
        timeout = 5000
    }
) => {
    let startTime = currentTimestamp();
    // 处理 baseUrl
    if ('' === baseURL) {
        let tempPath = path.toLowerCase();
        if (tempPath.startsWith('/lazyone')) {
            baseURL = global.cloudAddress;
        }
        else if (tempPath.startsWith('/lazymind')) {
            baseURL = global.mindAddress;
        }
        else if (tempPath.startsWith('/lazycloud')) {
            baseURL = global.managerAddress;
        }
        else if (tempPath.startsWith('/base')) {
            baseURL = global.baseAddress;
        }
        else if (tempPath.startsWith('/fishlog')) {
            baseURL = global.logAddress;
        }
        else if (tempPath.startsWith('/lazydevice')) {
            baseURL = global.deviceAddress;
        }
        else if (tempPath.startsWith('/common')) {
            baseURL = global.baseAddress;
        }
        else if (tempPath.startsWith('/regression')) {
            baseURL = global.baseAddress;
        }
        else if (tempPath.startsWith('/openapi')) {
            baseURL = global.openapiAddress;
        }
        else if (tempPath.startsWith('/auth')) {
            baseURL = global.baseAddress;
        }
        else if (tempPath.startsWith('/tree')) {
            baseURL = global.baseAddress;
        }
        else if (tempPath.startsWith('/icafe')) {
            baseURL = global.baseAddress;
        }
        else if (tempPath.startsWith('/lazyailab')) {
            baseURL = global.ailabAddress;
        }
        else {
            logger.warn(`[util.request] ${path} 兜底地址: ${global.defaultAddress}`);
            baseURL = global.defaultAddress;
        }
    }
    // 处理 method
    method = method.toLowerCase();
    // 做 cookie 前置处理
    if (!cookie) {
        cookie = global.cookie;
        if (!cookie) {
            cookie = await handleUuapCookie();
        }
    }
    headers.Cookie = cookie;
    // 处理请求体
    path = await _checkRequestPath(path, query);
    ({ body, headers } = await _checkRequestHeader(body, headers));
    // 发送请求
    let axiosItem = axios.create();
    let res = null;
    let error = null;
    try {
        if ('get' === method) {
            res = await _get(axiosItem, baseURL, path, body, headers, timeout);
        }
        else if ('post' === method) {
            res = await _post(axiosItem, baseURL, path, body, headers, timeout);
        }
        else if ('put' === method) {
            res = await _put(axiosItem, baseURL, path, body, headers, timeout);
        }
        else if ('delete' === method) {
            res = await _del(axiosItem, baseURL, path, body, headers, timeout);
        }
        else {
            throw new Error(`Do not support method: ${method}`);
        }
    }
    catch (err) {
        error = err;
    }
    finally {
        axiosItem = null;
        if (null !== error) {
            throw error;
        }
    }
    let cost = currentTimestamp() - startTime;
    if (cost > 1000) {
        logger.warn(`[util.request] ${path} 慢请求耗时 cost: ${cost}ms`);
    }
    return res;
};

module.exports = { cloudServerRequest };