/**
 * @create 王家麒@2022.05.19
 */
const { ENV, IS_DEBUG, ONLINE_DEBUG, PREVIEW_DEBUG } = require('./env');

const OFFLINE_ADDRESS = 'http://rmtc-offline.bcc-bdbl.baidu.com';
const PREVIEW_ADDRESS = 'https://pioneer.baidu-int.com';
const PREVIEW_SOCKET_ADDRESS = 'wss://pioneer.baidu-int.com';
const ONLINE_ADDRESS = 'https://qamate.baidu-int.com';
const ONLINE_SOCKET_ADDRESS = 'wss://qamate.baidu-int.com';

// 开发阶段与环境, 纯代码启动
if (IS_DEBUG) {
    global.defaultAddress = OFFLINE_ADDRESS;
    global.cloudAddress = OFFLINE_ADDRESS; // lazyone
    global.mindAddress = OFFLINE_ADDRESS; // lazymind
    global.baseAddress = OFFLINE_ADDRESS; // base
    global.deviceAddress = OFFLINE_ADDRESS; // lazydevice
    global.openapiAddress = OFFLINE_ADDRESS; // openapi
    global.ailabAddress = OFFLINE_ADDRESS; // lazyailab
    global.managerAddress = OFFLINE_ADDRESS; // lazycloud
    global.logAddress = 'http://lazy-mind.bcc-szzj.baidu.com:8088'; // fishlog
    global.dorkingAddress = 'ws://************:8060'; // dorking 云控系统
    // 以下为外部平台
    global.logcheckAddress = 'https://logcheck.baidu-int.com'; // logcheck 打点
    global.qadcAddress = 'http://qadc.baidu-int.com'; // qadc数据中台
    global.mockAddress = 'http://mock-api.baidu-int.com'; // mock 平台
    global.monkeyAgentAddress = 'http://************:8010';
    // 线上环境开发
    if (ONLINE_DEBUG) {
        global.defaultAddress = ONLINE_ADDRESS;
        global.cloudAddress = ONLINE_ADDRESS;
        global.mindAddress = ONLINE_ADDRESS;
        global.managerAddress = ONLINE_ADDRESS;
        global.baseAddress = ONLINE_ADDRESS;
        global.logAddress = ONLINE_ADDRESS;
        global.deviceAddress = ONLINE_ADDRESS;
        global.openapiAddress = ONLINE_ADDRESS;
        global.ailabAddress = ONLINE_ADDRESS;
        global.dorkingAddress = ONLINE_SOCKET_ADDRESS;
        // 以下为外部平台
        global.logcheckAddress = 'https://logcheck.baidu-int.com';
        global.qadcAddress = 'http://qadc.baidu-int.com';
        global.mockAddress = 'http://mock-api.baidu-int.com';
    }
    // 预览环境开发
    else if (PREVIEW_DEBUG) {
        global.defaultAddress = PREVIEW_ADDRESS;
        global.cloudAddress = PREVIEW_ADDRESS;
        global.mindAddress = PREVIEW_ADDRESS;
        global.deviceAddress = PREVIEW_ADDRESS;
        global.managerAddress = PREVIEW_ADDRESS;
        global.baseAddress = PREVIEW_ADDRESS;
        global.openapiAddress = PREVIEW_ADDRESS;
        global.ailabAddress = PREVIEW_ADDRESS;
        global.dorkingAddress = PREVIEW_SOCKET_ADDRESS;
        global.logAddress = 'http://lazy-mind.bcc-szzj.baidu.com:8088';
        // 以下为外部平台
        global.logcheckAddress = 'https://logcheck.baidu-int.com';
        global.qadcAddress = 'http://qadc.baidu-int.com';
        global.mockAddress = 'http://mock-api.baidu-int.com';
    }
}
else {
    // 兜底一个线上生产环境
    global.defaultAddress = ONLINE_ADDRESS;
    global.cloudAddress = ONLINE_ADDRESS;
    global.mindAddress = ONLINE_ADDRESS;
    global.managerAddress = ONLINE_ADDRESS;
    global.baseAddress = ONLINE_ADDRESS;
    global.logAddress = ONLINE_ADDRESS;
    global.deviceAddress = ONLINE_ADDRESS;
    global.openapiAddress = ONLINE_ADDRESS;
    global.ailabAddress = ONLINE_ADDRESS;
    global.dorkingAddress = ONLINE_SOCKET_ADDRESS;
    // 以下为外部平台
    global.logcheckAddress = 'https://logcheck.baidu-int.com';
    global.qadcAddress = 'http://qadc.baidu-int.com';
    global.mockAddress = 'http://mock-api.baidu-int.com';
    // 预览环境
    if ('preview' === ENV) {
        global.defaultAddress = PREVIEW_ADDRESS;
        global.cloudAddress = PREVIEW_ADDRESS;
        global.mindAddress = PREVIEW_ADDRESS;
        global.deviceAddress = PREVIEW_ADDRESS;
        global.managerAddress = PREVIEW_ADDRESS;
        global.baseAddress = PREVIEW_ADDRESS;
        global.openapiAddress = PREVIEW_ADDRESS;
        global.ailabAddress = PREVIEW_ADDRESS;
        global.dorkingAddress = PREVIEW_SOCKET_ADDRESS;
        global.logAddress = 'http://lazy-mind.bcc-szzj.baidu.com:8088';
        // 以下为外部平台
        global.logcheckAddress = 'https://logcheck.baidu-int.com';
        global.qadcAddress = 'http://qadc.baidu-int.com';
        global.mockAddress = 'http://mock-api.baidu-int.com';
    }
    // 测试环境
    if ('test' === ENV) {
        global.defaultAddress = OFFLINE_ADDRESS;
        global.cloudAddress = OFFLINE_ADDRESS;
        global.mindAddress = OFFLINE_ADDRESS;
        global.deviceAddress = OFFLINE_ADDRESS;
        global.baseAddress = OFFLINE_ADDRESS;
        global.openapiAddress = OFFLINE_ADDRESS;
        global.ailabAddress = OFFLINE_ADDRESS;
        global.managerAddress = 'http://************';
        global.logAddress = 'http://lazy-mind.bcc-szzj.baidu.com:8088';
        global.dorkingAddress = 'ws://*************:8060';
        // 以下为外部平台
        global.logcheckAddress = 'https://logcheck.baidu-int.com';
        global.qadcAddress = 'http://qadc.baidu-int.com';
        global.mockAddress = 'http://mock-api.baidu-int.com';
    }
}
global.superHeader = {
    'QAMate-Token': 'super-9a796933bf024ebfa0420dce6f864a51',
    'QAMate-ModuleId': 0
};

// 根据域名决定缓存目录
let caseDebugTag = 1;
if (ONLINE_ADDRESS === global.cloudAddress) {
    caseDebugTag = 0;
}
else if (PREVIEW_ADDRESS === global.cloudAddress) {
    caseDebugTag = 2;
}

module.exports = { CASE_DEBUG: caseDebugTag };
