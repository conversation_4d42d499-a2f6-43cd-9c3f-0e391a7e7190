import { useEffect, useState } from 'react';
import { useNavigate } from 'umi';
import { Button, Card, Descriptions, Form, Input, message, Radio, Spin, Select, Tag } from 'antd';
import { isEmpty } from 'lodash';
import { stringifyUrl } from 'query-string';
import EventBus from 'COMMON/utils/eventBus';
import { connectModel } from 'COMMON/middleware';
import { getQueryParams } from 'COMMON/utils/utils';
import baseModel from 'COMMON/models/baseModel';
import planModel from 'COMMON/models/planModel';
import commonModel from 'COMMON/models/commonModel';
import { CardContent } from 'COMMON/components/common/Card';
import { CardHeader } from 'COMMON/components/common/Card';
import { CardTitle } from 'COMMON/components/common/Card';
import { getEnvList } from 'COMMON/api/front_qe_tools/config';
import { getTreeNodeList } from 'COMMON/api/front_qe_tools/tree';
import { getGroupList } from 'COMMON/api/front_qe_tools/group';
import { getCaseTree } from 'COMMON/api/front_qe_tools/node';
import { createPerfPlanList } from 'COMMON/api/front_qe_tools/lazyperf';
import RetryTimes from 'FEATURES/front_qe_tools/plan/components/Setting/RetryTimes';
import LocalDevice from 'FEATURES/front_qe_tools/plan/components/Setting/LocalDevice';
import EnvParams from 'FEATURES/front_qe_tools/plan/components/Setting/EnvParams';
import styles from './CreatePlanPage.module.less';
import { v4 as uuidv4} from 'uuid';
// 策略配置选项
const STRATEGY_OPTIONS = {
    // 录制起始选项
    recordStart: [
        {
            value: 'case_0_start',
            label: '用例开始',
            color: '#52c41a',
            order: 0
        },
        {
            value: 'steps_0_start',
            label: '步骤0开始',
            color: '#1890ff',
            order: 1
        },
        {
            value: 'steps_1_start',
            label: '步骤1开始',
            color: '#1890ff',
            order: 2
        },
        {
            value: 'steps_2_start',
            label: '步骤2开始',
            color: '#1890ff',
            order: 3
        }
    ],
    // 首帧界定选项
    firstFrame: [
        {
            value: 'algorithm_0_crossdet',
            label: '算法校准 - 控件识别',
            color: '#eb2f96',
            order: -1 // 算法校准优先级最高
        },
        {
            value: 'case_0_start',
            label: '用例校准 - 用例开始',
            color: '#52c41a',
            order: 0
        },
        {
            value: 'steps_0_start',
            label: '步骤校准 - 步骤0开始',
            color: '#1890ff',
            order: 1
        },
        {
            value: 'steps_0_end',
            label: '步骤校准 - 步骤0结束',
            color: '#1890ff',
            order: 2
        },
        {
            value: 'steps_1_start',
            label: '步骤校准 - 步骤1开始',
            color: '#1890ff',
            order: 3
        },
        {
            value: 'steps_1_end',
            label: '步骤校准 - 步骤1结束',
            color: '#1890ff',
            order: 4
        },
        {
            value: 'steps_2_start',
            label: '步骤校准 - 步骤2开始',
            color: '#1890ff',
            order: 5
        },
        {
            value: 'steps_2_end',
            label: '步骤校准 - 步骤2结束',
            color: '#1890ff',
            order: 6
        }
    ],
    // 尾帧界定选项
    lastFrame: [
        {
            value: 'algorithm_0_crossdet',
            label: '算法校准 - 控件识别',
            color: '#eb2f96',
            order: -1
        },
        {
            value: 'steps_0_end',
            label: '步骤校准 - 步骤0结束',
            color: '#1890ff',
            order: 2
        },
        {
            value: 'steps_1_end',
            label: '步骤校准 - 步骤1结束',
            color: '#1890ff',
            order: 4
        },
        {
            value: 'steps_2_end',
            label: '步骤校准 - 步骤2结束',
            color: '#1890ff',
            order: 6
        },
        {
            value: 'case_0_end',
            label: '用例校准 - 用例结束',
            color: '#52c41a',
            order: 10
        }
    ]
};

// 获取策略的执行顺序
const getStrategyOrder = (strategy) => {
    // 查找策略在配置中的顺序
    const allStrategies = [...STRATEGY_OPTIONS.recordStart, ...STRATEGY_OPTIONS.firstFrame, ...STRATEGY_OPTIONS.lastFrame];
    const strategyConfig = allStrategies.find(item => item.value === strategy);
    return strategyConfig ? strategyConfig.order : 999;
};

// 验证策略选择是否合理（首帧应该在录制起始之后或同时，尾帧应该在首帧之后或同时）
const validateStrategyOrder = (recordStart, firstFrame, lastFrame) => {
    const recordOrder = getStrategyOrder(recordStart);
    const firstOrder = getStrategyOrder(firstFrame);
    const lastOrder = getStrategyOrder(lastFrame);

    // 算法校准特殊处理
    if (firstFrame && firstFrame.startsWith('algorithm_')) {
        return true; // 算法校准可以与任何录制起始配合
    }

    if (lastFrame && lastFrame.startsWith('algorithm_')) {
        return true; // 算法校准可以作为任何尾帧
    }

    // 机制校准需要满足顺序关系
    if (recordStart && firstFrame && recordOrder > firstOrder) {
        return false;
    }

    if (firstFrame && lastFrame && firstOrder > lastOrder) {
        return false;
    }

    return true;
};

// 根据录制起始策略过滤可用的首帧策略
const getAvailableFirstFrameOptions = (recordStart) => {
    if (!recordStart) return STRATEGY_OPTIONS.firstFrame;

    const recordOrder = getStrategyOrder(recordStart);
    return STRATEGY_OPTIONS.firstFrame.filter(option => {
        // 算法校准总是可用
        if (option.value.startsWith('algorithm_')) return true;

        const optionOrder = getStrategyOrder(option.value);
        return optionOrder >= recordOrder;
    });
};

// 根据首帧策略过滤可用的尾帧策略
const getAvailableLastFrameOptions = (firstFrameStrategy) => {
    if (!firstFrameStrategy) return STRATEGY_OPTIONS.lastFrame;

    // 算法校准的首帧可以配合任何尾帧
    if (firstFrameStrategy.startsWith('algorithm_')) {
        return STRATEGY_OPTIONS.lastFrame;
    }

    const firstOrder = getStrategyOrder(firstFrameStrategy);
    return STRATEGY_OPTIONS.lastFrame.filter(option => {
        // 算法校准总是可用
        if (option.value.startsWith('algorithm_')) return true;

        const optionOrder = getStrategyOrder(option.value);
        return optionOrder >= firstOrder;
    });
};

function CreatePlanPage(props) {
    const { currentSpace } = props;
    const query = getQueryParams();
    const [deviceType, setDeviceType] = useState('1');
    const [loading, setLoading] = useState(true);
    const [groupId, setGroupId] = useState(null);
    const [directoryTreeData, setDirectoryTreeData] = useState([]);
    const [stepInfoList, setStepInfoList] = useState([]);
    const [treeNodeId, setTreeNodeId] = useState(null);
    const [caseNode, setCaseNode] = useState(null);
    const [sceneListMap, setSceneListMap] = useState({});
    const [recordStartMap, setRecordStartMap] = useState({}); // 录制起始策略映射
    const [createLoading, setCreateLoading] = useState(false);
    const [groupList, setGroupList] = useState([]);
    const [envList, setEnvList] = useState([]); // 环境列表
    const [createPlanOption, setCreatePlanOption] = useState({
        planName: null,
        executeConfig: {
            android: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                taskTimeout: 20,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            ios: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                taskTimeout: 20,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            server: {
                deviceType: null,
                deviceId: null,
                envParams: {}
            }
        }
    });
    const [configForm] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();
    const navigate = useNavigate();

    const handleChangeDeviceType = (e) => {
        const newDeviceType = e.target.value;
        setDeviceType(newDeviceType);
        // 同步更新表单值
        configForm.setFieldsValue({
            deviceType: newDeviceType
        });
    };

    const handleChangeGroupId = (value) => {
        setGroupId(value);
        setTreeNodeId(null);
        setCaseNode(null);
        setStepInfoList([]);
        configForm.setFieldsValue({
            treeNodeId: undefined,
            caseNodeId: undefined
        });
    };

    const handleChangeTreeNodeId = (value) => {
        console.log('value-handleChangeTreeNodeId', value);
        console.log('directoryTreeData', directoryTreeData);
        setTreeNodeId(value);
        setCaseNode(null);
        setStepInfoList([]);
        configForm.setFieldsValue({
            caseNodeId: undefined
        });
    };

    const handleChangeCaseNodeId = (value) => {
        const stepInfo = getStepInfoByCaseNodeIds(caseNode, value);
        console.log('打印-stepInfo', stepInfo, caseNode);

        // 使用更稳定的方式保留场景数据：基于 caseNodeId 而不是索引
        const newSceneListMap = {};

        // 将当前的场景数据按 caseNodeId 重新组织
        const sceneDataByCaseNodeId = {};
        stepInfoList.forEach((step, index) => {
            if (sceneListMap[index]) {
                sceneDataByCaseNodeId[step.caseNodeId] = sceneListMap[index];
            }
        });

        // 保留录制起始数据
        const recordStartDataByCaseNodeId = {};
        stepInfoList.forEach((step, index) => {
            if (recordStartMap[index]) {
                recordStartDataByCaseNodeId[step.caseNodeId] = recordStartMap[index];
            }
        });

        // 为新的 stepInfo 重新构建场景映射和录制起始映射
        const newRecordStartMap = {};
        stepInfo.forEach((step, newIndex) => {
            if (sceneDataByCaseNodeId[step.caseNodeId]) {
                // 保留已有的场景数据
                newSceneListMap[newIndex] = sceneDataByCaseNodeId[step.caseNodeId];
            }

            if (recordStartDataByCaseNodeId[step.caseNodeId]) {
                // 保留已有的录制起始数据
                newRecordStartMap[newIndex] = recordStartDataByCaseNodeId[step.caseNodeId];
            } else {
                // 新用例使用默认的录制起始策略
                newRecordStartMap[newIndex] = 'case_0_start';
            }
        });

        setStepInfoList(stepInfo);
        setSceneListMap(newSceneListMap);
        setRecordStartMap(newRecordStartMap);
    };

    // 添加场景
    const handleAddScene = (stepInfoIndex) => {
        const currentScenes = sceneListMap[stepInfoIndex] || [];
        const newScene = {
            id: uuidv4(),
            name: `场景 - ${currentScenes.length + 1}`,
            firstFrameStrategy: '', // 默认算法校准
            lastFrameStrategy: '' // 默认用例校准结束
        };
        setSceneListMap({
            ...sceneListMap,
            [stepInfoIndex]: [...currentScenes, newScene]
        });
    };

    // 删除场景
    const handleDeleteScene = (stepInfoIndex, sceneId) => {
        const currentScenes = sceneListMap[stepInfoIndex] || [];
        setSceneListMap({
            ...sceneListMap,
            [stepInfoIndex]: currentScenes.filter((scene) => scene.id !== sceneId)
        });
    };

    useEffect(() => {
        async function func() {
            if (!currentSpace?.id) {
                return;
            }
            // 获取目录树
            let { groupList } = await getGroupList({
                moduleId: currentSpace?.id,
                isArchive: 0
            });
            console.log('groupList', groupList);
            if (isEmpty(groupList)) {
                messageApi.error('获取目录树失败');
                return;
            }
            setGroupList(groupList);
            // setGroupId(groupList[0]?.groupId);
        }
        func();
    }, [currentSpace?.id, query?.planType]);

    useEffect(() => {
        async function func() {
            if (!groupId) {
                return;
            }
            let { tree } = await getTreeNodeList({ groupId: groupId });
            if (isEmpty(tree)) {
                messageApi.error('获取目录树失败');
                return;
            }
            setDirectoryTreeData(tree);
        }
        func();
    }, [groupId]);

    useEffect(() => {
        async function func() {
            if (!treeNodeId) {
                return;
            }
            const caseRootId = directoryTreeData.find(
                (item) => item?.nodeId === treeNodeId
            )?.caseRootId;
            if (!caseRootId) {
                return;
            }
            let res = await getCaseTree({ caseRootId: caseRootId, withSign: false });
            console.log('打印res', res);
            setCaseNode(res);
        }
        func();
    }, [treeNodeId]);

    useEffect(() => {
        obtaionEnvParamsList();
        // 初始化表单的设备类型值
        configForm.setFieldsValue({
            deviceType: '1'
        });
    }, []);

    // 监听表单值变化，同步更新 deviceType 状态
    useEffect(() => {
        const deviceTypeValue = configForm.getFieldValue('deviceType');
        if (deviceTypeValue && deviceTypeValue !== deviceType) {
            setDeviceType(deviceTypeValue);
        }
    }, [configForm]);

    // 获取运行环境列表
    const obtaionEnvParamsList = async () => {
        try {
            if (!currentSpace?.id) {
                return;
            }
            let androidRes = await getEnvList({
                moduleId: currentSpace?.id,
                osType: 1
            });
            let iosRes = await getEnvList({
                moduleId: currentSpace?.id,
                osType: 2
            });
            let serverRes = await getEnvList({
                moduleId: currentSpace?.id,
                osType: 4
            });
            setEnvList({
                android: androidRes.envList,
                ios: iosRes.envList,
                server: serverRes?.envList
            });
        } catch (err) {
            message.error(err?.message ?? err);
        }
    };

    const handleCreatePlan = () => {
        configForm
            ?.validateFields()
            .then(async (values) => {
                let body = {
                    moduleId: currentSpace?.id,
                    name: values.name,
                    type: +values.type,
                    // 用例参数
                    caseNodeParams: {
                        treeNodeId: treeNodeId,
                        osType: +values.deviceType,
                        caseNodeList:
                            values.caseNodeId?.map((caseNodeId, index) => {
                                const scenes = sceneListMap[index] || [];
                                return {
                                    caseNodeId: caseNodeId,
                                    recordStartStrategy: recordStartMap[index] || 'case_0_start',
                                    sceneList: scenes.map((scene) => ({
                                        name: scene.name,
                                        firstFrameStrategy: scene.firstFrameStrategy,
                                        lastFrameStrategy: scene.lastFrameStrategy
                                    }))
                                };
                            }) || []
                    },
                    // 执行参数
                    cloudParams: {
                        type: +values.deviceType === 1 ? 8 : 9, // 8-Android 速度评测任务 9-IOS 速度评测任务
                        deviceId: values.localDevice?.[0] || null, // 从表单的 localDevice 字段获取设备ID
                        executeTimes: +values.executeTimes,
                        envParams: (() => {
                            const envParamsData = +values.deviceType === 1
                                ? createPlanOption.executeConfig.android.envParams
                                : createPlanOption.executeConfig.ios.envParams;

                            // 如果有 envId，按照接口文档格式传递
                            if (envParamsData?.envId) {
                                return {
                                    envId: envParamsData.envId
                                };
                            }

                            // 如果没有 envId 但有环境详情，使用 envDetail 格式
                            if (envParamsData?.paramList || envParamsData?.appList || envParamsData?.serverList) {
                                return {
                                    envDetail: {
                                        paramList: (envParamsData.paramList || []).map(item => ({
                                            paramKey: item.paramKey,
                                            envValue: item.envValue || item.paramValue
                                        })),
                                        appList: (envParamsData.appList || []).map(item => ({
                                            appId: item.appId,
                                            envValue: item.envValue || item.packageList?.[0]
                                        })),
                                        serverList: (envParamsData.serverList || []).map(item => ({
                                            serverId: item.serverId,
                                            envValue: item.envValue || item.addressList?.[0]
                                        }))
                                    }
                                };
                            }

                            // 默认返回空的 envDetail
                            return {
                                envDetail: {
                                    paramList: [],
                                    appList: [],
                                    serverList: []
                                }
                            };
                        })()
                    }
                };

                console.log('创建计划的请求体:', body);
                setCreateLoading(true);
                createPerfPlanList(body)
                    .then((_res) => {
                        messageApi.success('任务创建成功');
                        EventBus.emit('refreshPerfPlanList');
                        navigate(
                            stringifyUrl({
                                url: '/lazyperf/index',
                                query: {
                                    moduleId: currentSpace?.id,
                                    planId: 111,
                                    planType: 1,
                                    stage: 'task'
                                }
                            })
                        );
                        setCreateLoading(false);
                    })
                    .catch(() => {
                        setCreateLoading(false);
                    });
            })
            .catch((err) => {
                console.log(err?.message ?? err);
                setCreateLoading(false);
                messageApi.warning('请填写完整表单');
            });
    };

    const groupOptions = () => {
        console.log('group', groupList);
        return groupList.map((group) => ({
            value: group.groupId,
            key: group.groupId,
            label: group.groupName
        }));
    };

    const directoryTreeDataOptions = (tree = directoryTreeData) => {
        console.log('directoryTreeData', tree);

        let result = [];
        tree.forEach((item) => {
            if (item?.nodeType === 2) {
                result.push({
                    value: item?.nodeId,
                    key: item?.nodeId,
                    label: item?.nodeName
                });
            }
            if (Array.isArray(item?.children) && item.children.length) {
                result = result.concat(directoryTreeDataOptions(item.children));
            }
        });
        return result;
    };

    const caseNodeOptions = (tree = caseNode) => {
        if (!tree) {
            return [];
        }
        const arr = Array.isArray(tree) ? tree : [tree];
        let result = [];
        arr.forEach((item) => {
            if (item && item.nodeName && item.caseNodeId) {
                result.push({
                    value: item?.caseNodeId,
                    key: item?.caseNodeId,
                    label: item?.nodeName
                });
            }
            if (Array.isArray(item.children) && item.children.length > 0) {
                result = result.concat(caseNodeOptions(item.children));
            }
        });
        return result;
    };

    const getStepInfoByCaseNodeIds = (caseNode, caseNodeIds) => {
        const nodesArray = Array.isArray(caseNode) ? caseNode : [caseNode];
        const searchNodes = (nodes, ids) => {
            let result = [];
            for (const node of nodes) {
                if (ids.includes(node.caseNodeId)) {
                    result.push({
                        caseNodeId: node.caseNodeId,
                        caseNodeName: node.nodeName,
                        stepInfo: node.extra.stepInfo
                    });
                }
                if (!isEmpty(node.children)) {
                    result = result.concat(searchNodes(node.children, ids));
                }
            }

            return result;
        };
        return searchNodes(nodesArray, caseNodeIds);
    };

    console.log('stepInfoList', stepInfoList);
    console.log('deviceType', deviceType);
    console.log('envList', envList);
    console.log('envList for current deviceType', envList?.[+deviceType === 2 ? 'ios' : 'android']);

    return (
        <Spin spinning={createLoading}>
            <CardContent>
                {contextHolder}
                <Descriptions column={1} size="small" />
                <div className={styles.header}>
                    <CardHeader
                        text={'新建计划'}
                        extra={
                            <>
                                <Button type="primary" onClick={handleCreatePlan}>
                                    创建
                                </Button>
                                <Button
                                    onClick={() => {
                                        navigate(-1);
                                    }}
                                >
                                    取消
                                </Button>
                            </>
                        }
                    />
                </div>
                <div className={styles.container}>
                    <Form form={configForm} layout="horizontal" requiredMark={false} colon={false}>
                        <CardTitle text="任务配置" />
                        <Form.Item name="name" label="任务名称">
                            <Input placeholder="请输入任务名称" allowClear />
                        </Form.Item>
                        <Form.Item name="type" label="评测类型" initialValue={'1'.toString()}>
                            <Radio.Group>
                                <Radio value="1">速度评测</Radio>
                                <Radio value="2">指标评测</Radio>
                            </Radio.Group>
                        </Form.Item>
                        <CardTitle text="设备配置" />
                        <Form.Item name="deviceType" label="设备类型" initialValue={'1'}>
                            <Radio.Group onChange={handleChangeDeviceType}>
                                <Radio value="1">Android</Radio>
                                <Radio value="2">iOS</Radio>
                            </Radio.Group>
                        </Form.Item>
                        <LocalDevice form={configForm} osType={deviceType} />
                        <RetryTimes
                            form={configForm}
                            label="执行次数"
                            min={1}
                            max={99}
                            name="executeTimes"
                            initialValue={1}
                            required
                            tooltip="最大执行次数，范围：1-99"
                            placeholder="请设置执行次数"
                        />
                        <EnvParams
                            form={configForm}
                            osType={+deviceType}
                            envList={envList?.[+deviceType === 2 ? 'ios' : 'android'] || []}
                            createPlanOption={createPlanOption}
                            setCreatePlanOption={setCreatePlanOption}
                        />
                        <CardTitle text="用例配置" />
                        <div
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '10px'
                            }}
                        >
                            <Form.Item label="执行用例" name="groupId">
                                <Select
                                    style={{ width: 150 }}
                                    className={styles.selecPlanType}
                                    options={groupOptions()}
                                    allowClear
                                    placeholder="请选择用例组"
                                    onChange={handleChangeGroupId}
                                />
                            </Form.Item>
                            <Form.Item name="treeNodeId">
                                <Select
                                    style={{ width: 150 }}
                                    className={styles.selecPlanType}
                                    options={directoryTreeDataOptions()}
                                    allowClear
                                    placeholder="请选择目录树"
                                    onChange={handleChangeTreeNodeId}
                                />
                            </Form.Item>
                            <Form.Item name="caseNodeId">
                                <Select
                                    mode="multiple"
                                    style={{ width: 250 }}
                                    className={styles.selecPlanType}
                                    options={caseNodeOptions()}
                                    allowClear
                                    placeholder="请选择执行用例"
                                    onChange={handleChangeCaseNodeId}
                                />
                            </Form.Item>
                        </div>

                        {/* 当选择执行用例后显示的场景配置 */}
                        {stepInfoList && stepInfoList.length > 0 && (
                            <>
                                {stepInfoList.map((stepInfo, index) => {
                                    console.log('stepInfo', stepInfo);
                                    return (
                                        <Card
                                            key={stepInfo.caseNodeId}
                                            title={`${stepInfo.caseNodeName || `用例${index + 1}`}`}
                                            size="small"
                                            style={{ marginBottom: '24px' }}
                                        >
                                            <div
                                                style={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: '16px',
                                                    marginBottom: '16px'
                                                }}
                                            >
                                                <Tag
                                                    color="blue"
                                                    style={{
                                                        cursor: 'pointer'
                                                    }}
                                                    onClick={() => handleAddScene(index)}
                                                >
                                                    + 添加场景
                                                </Tag>
                                                <div style={{ minWidth: '200px' }}>
                                                    <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
                                                        录制起始
                                                    </label>
                                                    <Select
                                                        style={{ width: '100%' }}
                                                        placeholder="请选择录制起始策略"
                                                        value={recordStartMap[index] || 'case_0_start'}
                                                        onChange={(value) => {
                                                            // 更新录制起始策略
                                                            setRecordStartMap({
                                                                ...recordStartMap,
                                                                [index]: value
                                                            });

                                                            // 检查并调整已有场景的首帧策略
                                                            const currentScenes = sceneListMap[index] || [];
                                                            const updatedScenes = currentScenes.map(scene => {
                                                                const updatedScene = { ...scene };

                                                                // 如果当前首帧策略与新的录制起始不兼容，自动调整
                                                                if (scene.firstFrameStrategy &&
                                                                    !validateStrategyOrder(value, scene.firstFrameStrategy, scene.lastFrameStrategy)) {
                                                                    // 找到第一个可用的首帧策略
                                                                    const availableOptions = getAvailableFirstFrameOptions(value);
                                                                    if (availableOptions.length > 0) {
                                                                        updatedScene.firstFrameStrategy = availableOptions[0].value;

                                                                        // 同时检查尾帧策略是否需要调整
                                                                        if (scene.lastFrameStrategy &&
                                                                            !validateStrategyOrder(value, updatedScene.firstFrameStrategy, scene.lastFrameStrategy)) {
                                                                            const availableLastOptions = getAvailableLastFrameOptions(updatedScene.firstFrameStrategy);
                                                                            if (availableLastOptions.length > 0) {
                                                                                updatedScene.lastFrameStrategy = availableLastOptions[0].value;
                                                                            }
                                                                        }
                                                                    }
                                                                }

                                                                return updatedScene;
                                                            });

                                                            if (updatedScenes.length > 0) {
                                                                setSceneListMap({
                                                                    ...sceneListMap,
                                                                    [index]: updatedScenes
                                                                });
                                                            }
                                                        }}
                                                        options={STRATEGY_OPTIONS.recordStart}
                                                    />
                                                </div>
                                            </div>
                                            {(sceneListMap[index] || []).map((scene) => (
                                                <Card
                                                    key={scene.id}
                                                    size="small"
                                                    style={{ marginBottom: '12px' }}
                                                >
                                                    <div style={{ marginBottom: '16px' }}>
                                                        <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
                                                            场景名称
                                                        </label>
                                                        <Input
                                                            placeholder="请输入场景名称"
                                                            value={scene.name || ''}
                                                            onChange={(e) => {
                                                                const updatedScenes = sceneListMap[index].map(s =>
                                                                    s.id === scene.id ? { ...s, name: e.target.value } : s
                                                                );
                                                                setSceneListMap({
                                                                    ...sceneListMap,
                                                                    [index]: updatedScenes
                                                                });
                                                            }}
                                                        />
                                                    </div>

                                                    <div style={{ marginBottom: '16px' }}>
                                                        <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
                                                            首帧界定
                                                        </label>
                                                        <Select
                                                            className={styles.selecPlanType}
                                                            style={{ width: '100%' }}
                                                            placeholder="请选择首帧界定策略"
                                                            value={scene.firstFrameStrategy || undefined}
                                                            onChange={(value) => {
                                                                const recordStart = recordStartMap[index] || 'case_0_start';

                                                                // 验证策略选择是否合理
                                                                if (!validateStrategyOrder(recordStart, value, scene.lastFrameStrategy)) {
                                                                    message.warning('首帧策略不能早于录制起始策略执行');
                                                                    return;
                                                                }

                                                                const updatedScenes = sceneListMap[index].map(s => {
                                                                    if (s.id === scene.id) {
                                                                        const updatedScene = { ...s, firstFrameStrategy: value };

                                                                        // 如果首帧策略变化导致尾帧策略不兼容，自动调整尾帧策略
                                                                        if (s.lastFrameStrategy &&
                                                                            !validateStrategyOrder(recordStart, value, s.lastFrameStrategy)) {
                                                                            const availableLastOptions = getAvailableLastFrameOptions(value);
                                                                            if (availableLastOptions.length > 0) {
                                                                                updatedScene.lastFrameStrategy = availableLastOptions[0].value;
                                                                            }
                                                                        }
                                                                        return updatedScene;
                                                                    }
                                                                    return s;
                                                                });
                                                                setSceneListMap({
                                                                    ...sceneListMap,
                                                                    [index]: updatedScenes
                                                                });
                                                            }}
                                                            options={getAvailableFirstFrameOptions(recordStartMap[index] || 'case_0_start')}
                                                        />
                                                    </div>

                                                    <div style={{ marginBottom: '16px' }}>
                                                        <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
                                                            尾帧界定
                                                        </label>
                                                        <Select
                                                            className={styles.selecPlanType}
                                                            style={{ width: '100%' }}
                                                            placeholder="请选择尾帧界定策略"
                                                            value={scene.lastFrameStrategy || undefined}
                                                            onChange={(value) => {
                                                                const recordStart = recordStartMap[index] || 'case_0_start';
                                                                const currentScene = sceneListMap[index].find(s => s.id === scene.id);

                                                                // 验证策略选择是否合理
                                                                if (!validateStrategyOrder(recordStart, currentScene.firstFrameStrategy, value)) {
                                                                    message.warning('尾帧策略不能早于首帧策略执行');
                                                                    return;
                                                                }

                                                                const updatedScenes = sceneListMap[index].map(s =>
                                                                    s.id === scene.id ? { ...s, lastFrameStrategy: value } : s
                                                                );
                                                                setSceneListMap({
                                                                    ...sceneListMap,
                                                                    [index]: updatedScenes
                                                                });
                                                            }}
                                                            options={scene.firstFrameStrategy ? getAvailableLastFrameOptions(scene.firstFrameStrategy) : STRATEGY_OPTIONS.lastFrame}
                                                        />
                                                    </div>

                                                    <Tag
                                                        color="red"
                                                        style={{
                                                            cursor: 'pointer'
                                                        }}
                                                        onClick={() =>
                                                            handleDeleteScene(index, scene.id)
                                                        }
                                                    >
                                                        删除场景
                                                    </Tag>
                                                </Card>
                                            ))}
                                        </Card>
                                    );
                                })}
                            </>
                        )}
                    </Form>
                </div>
            </CardContent>
        </Spin>
    );
}

export default connectModel([baseModel, planModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentModule: state.common.base.currentModule
}))(CreatePlanPage);
