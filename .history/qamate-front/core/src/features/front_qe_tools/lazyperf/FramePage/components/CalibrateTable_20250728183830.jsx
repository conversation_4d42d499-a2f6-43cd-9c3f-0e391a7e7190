import { Space, Table, Tag } from 'antd';

const columns = [
    {
        title: 'ID',
        dataIndex: 'id',
        key: 'id'
    },
    {
        title: '智能TTI',
        dataIndex: 'auto',
        key: 'auto'
    },
    {
        title: '人工TTI',
        dataIndex: 'manual',
        key: 'manual'
    },
    {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (status) => {
            let color;
            switch (status) {
                case '智能校准':
                    color = 'processing';
                    break;
                case '人工校准':
                    color = 'success';
                    break;
                case '已废弃':
                    color = 'error';
                    break;
                case '未校准':
                default:
                    color = 'default';
                    break;
            }
            return <Tag color={color}>{status}</Tag>;
        }
    },
    {
        title: '操作',
        key: 'action',
        render: () => (
            <Space size="middle">
                <a>校准</a>
            </Space>
        )
    }
];

const CalibrateTable = ({ recordList = [] }) => {
    const transformedData = recordList.map((record, index) => {
        const { correctDetail = {}, recordSceneId, isValid } = record;
        const { auto = {}, manual = {} } = correctDetail;

        const autoTTI =
            auto?.lastFrameTimestamp && auto?.firstFrameTimestamp
                ? `${auto?.lastFrameTimestamp - auto?.firstFrameTimestamp} ms`
                : '-';
        const manualTTI =
            manual?.lastFrameTimestamp && manual?.firstFrameTimestamp
                ? `${manual?.lastFrameTimestamp - manual?.firstFrameTimestamp} ms`
                : '-';
        let status = '未校准';
        if (manual?.firstFrameStatus === 1 || manual?.lastFrameStatus === 1) {
            status = '人工校准';
        } else if (auto?.firstFrameStatus === 1 || auto?.lastFrameStatus === 1) {
            status = '智能校准';
        }
        return {
            key: recordSceneId,
            id: recordSceneId,
            auto: autoTTI,
            manual: manualTTI,
            status: status
        };
    });

    return <Table columns={columns} dataSource={transformedData} />;
};

export default CalibrateTable;
