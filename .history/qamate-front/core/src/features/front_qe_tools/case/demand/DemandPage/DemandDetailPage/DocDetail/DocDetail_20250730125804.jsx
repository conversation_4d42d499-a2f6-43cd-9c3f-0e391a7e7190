import { useEffect, useState } from 'react';
import { Divider, Layout, message } from 'antd';
import zlib from 'zlib';
import { isEmpty } from 'lodash';
import { EditOutlined, RetweetOutlined } from '@ant-design/icons';
import { <PERSON><PERSON><PERSON>, Editor, createEditor, Toolbar, useStable } from '@baidu/morpho';
import { TOOLBAR_MENUS } from 'COMMON/utils/editorUtils';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import demandModel from 'COMMON/models/demandModel';
import { getDocInfoFromUrl } from 'FEATURES/front_qe_tools/case/demand/utils';
import { fileImageUpload, escape2Html } from 'COMMON/utils/utils';
import { getBosToken } from 'COMMON/api/base/common';
import {
    updateTreeNodeDoc,
    updateTreeNodeDocContent,
    getIcafeRelationDetail
} from 'COMMON/api/front_qe_tools/demand';
import Loading from 'COMMON/components/common/Loading';
import { updateDocContent } from 'FEATURES/components/demand/GenerateCase/utils';
import styles from './DocDetail.module.less';

const { Header, Content } = Layout;

function DocDetail(props) {
    const { currentDoc, setCurrentDoc, kuSDK, docList, setDocList, nextStepComponent } = props;
    const [isEdit, setIsEdit] = useState(false);
    const [loading, setLoading] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const editorMd = useStable(() =>
        createEditor({
            image: {
                upload: fileImageUpload,
                base64: false
            },
            embed: false
        })
    );
    const [inputValue, setInputValue] = useState(editorMd.htmlSerializer.deserialize('<div/>'));

    useEffect(() => {
        async function func() {
            try {
                if (isEmpty(currentDoc?.nodeContent)) {
                    setInputValue(editorMd.htmlSerializer.deserialize('<div/>'));
                    return;
                }
                let bosUrl = currentDoc?.nodeContent;
                let res = await getBosToken({ bosLink: currentDoc?.nodeContent });
                if (!res?.token) {
                    messageApi.error('身份校验失败，获取图片失败');
                }
                bosUrl += '?authorization=' + res.token;
                if (!bosUrl?.includes('.knowledge')) {
                    let docContent = await fetch(bosUrl).then((r) => r.text());
                    // html 格式
                    // 反转义
                    if (!docContent?.startsWith('<')) {
                        docContent = escape2Html(docContent);
                    }
                    // 转 json 格式
                    let jsonContent = editorMd?.htmlSerializer?.deserialize(docContent);

                    setInputValue(jsonContent);
                    setLoading(false);
                } else {
                    let docContent = await fetch(bosUrl).then((r) => r.json());
                    // json 格式
                    setInputValue(docContent);
                    setLoading(false);
                }
            } catch (err) {
                console.log(err?.message ?? err);
                setLoading(false);
            }
        }
        func();
    }, [currentDoc?.docNodeId]);

    const handleUpdateDoc = (title) => {
        let params = {
            docNodeId: currentDoc?.docNodeId,
            nodeName: title
        };
        updateTreeNodeDoc(params).then(() => {
            setIsEdit(false);
        });
    };

    const handleUpdateDocDetail = (json) => {
        let params = {
            docNodeId: currentDoc?.docNodeId,
            contentType: 'json',
            nodeContent: json
        };
        zlib.gzip(JSON.stringify(params), async (error, zipBuf) => {
            if (error) {
                messageApi.error(error);
                return;
            }
            let res = await updateTreeNodeDocContent(zipBuf, {
                headers: { 'Content-Encoding': 'gzip' }
            });
            setCurrentDoc({
                ...currentDoc,
                nodeContent: res?.nodeContent
            });
            setDocList(updateDocContent(docList, currentDoc?.docNodeId, res?.nodeContent));
            messageApi.success('更新成功');
            setIsEdit(false);
        });
    };

    const handleRefreshDoc = async () => {
        try {
            setLoading(true);
            if (currentDoc?.nodeType === 1) {
                let res = await getIcafeRelationDetail({ icafeLinkList: [currentDoc?.nodeLink] });
                handleUpdateDoc(res[currentDoc?.nodeLink]?.title);
                let detail = res[currentDoc?.nodeLink]?.detail;
                // html 反转义
                if (!detail?.startsWith('<')) {
                    detail = escape2Html(detail);
                }
                detail = editorMd.htmlSerializer.deserialize(detail);
                handleUpdateDocDetail(detail);
                setInputValue(detail);
            } else {
                let docInfo = getDocInfoFromUrl(currentDoc.nodeLink);
                if (isEmpty(kuSDK)) {
                    message.error('知识库 SDK 获取失败');
                    setLoading(false);
                    return;
                }
                let doc = await kuSDK?.getDocContent({
                    spaceGuid: docInfo.spaceGuid,
                    groupGuid: docInfo.groupGuid,
                    repositoryGuid: docInfo.repositoryGuid,
                    docGuid: docInfo.docGuid,
                    type: 'json',
                    brCharacterInTable: ' '
                });
                handleUpdateDoc(
                    doc?.content?.children?.find((item) => item.type === 'title')?.children?.[0]
                        ?.text ?? currentDoc.nodeLink
                );
                handleUpdateDocDetail(doc?.content?.children);
                console.log('doc?.content?.children', doc?.content?.children);
                setInputValue(doc?.content?.children);
            }
            setLoading(false);
        } catch (err) {
            console.log(err?.message ?? err);
            setLoading(false);
        }
    };

    if (loading) {
        return <Loading />;
    }

    return (
        <Layout className={styles.right}>
            {contextHolder}
            <Header className={styles.header}>
                <div className={styles.titleInfo}>
                    <div className={styles.headerTitle}>{currentDoc?.nodeName}</div>
                    <div className={styles.headerBtn}>
                        {isEdit ? (
                            <div className={styles.btnGroup}>
                                <span
                                    className={styles.btn}
                                    onClick={() => {
                                        console.log('inputValue', inputValue);
                                        handleUpdateDocDetail(inputValue);
                                    }}
                                >
                                    <RetweetOutlined className={styles.btnIcon} />
                                    更新
                                </span>
                                <Divider type="vertical" />
                                <span className={styles.btn} onClick={() => setIsEdit(false)}>
                                    退出
                                </span>
                            </div>
                        ) : (
                            <div className={styles.btnContainer}>
                                <div
                                    className={`${styles.btnGroup} ${styles.editBtnGroup}`}
                                    onClick={() => setIsEdit(true)}
                                >
                                    <EditOutlined className={styles.btnIcon} />
                                    <span className={styles.btn}>编辑</span>
                                </div>
                                {nextStepComponent && (
                                    <div className={styles.nextStepBtnWrapper}>
                                        {nextStepComponent}
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </div>
                <div className={styles.linkInfo}>
                    <span>{currentDoc?.nodeType === 2 ? '知识库链接' : 'iCafe 链接'}:&nbsp;</span>
                    <div className={styles.linkUrl}>
                        <a
                            href={
                                currentDoc?.nodeType === 2
                                    ? currentDoc?.nodeLink
                                    : 'https://console.cloud.baidu-int.com/devops/icafe/issue/' +
                                      currentDoc?.nodeLink +
                                      '/show?source=copy-shortcut'
                            }
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            {currentDoc?.nodeType === 2
                                ? currentDoc?.nodeLink
                                : 'https://console.cloud.baidu-int.com/devops/icafe/issue/' +
                                  currentDoc?.nodeLink +
                                  '/show?source=copy-shortcut'}
                        </a>
                    </div>
                    <div className={styles.linkRefresh}>
                        <a onClick={handleRefreshDoc}>
                            基于链接刷新
                            {currentDoc?.nodeType === 2 ? '知识库需求' : '需求文档'}
                        </a>
                    </div>
                </div>
            </Header>
            <Content className={styles.content}>
                <div className={styles.editorContainer}>
                    <Morpho editor={editorMd}>
                        <Toolbar className={styles.editorToolbar} menus={TOOLBAR_MENUS} />
                        <Editor
                            style={{
                                overflow: 'scroll',
                                margin: '30px 0 0 30px'
                            }}
                            autoFocus
                            value={inputValue}
                            onChange={setInputValue}
                            readOnly={!isEdit}
                        />
                    </Morpho>
                </div>
            </Content>
        </Layout>
    );
}

export default connectModel([baseModel, demandModel, commonModel], (state) => ({
    kuSDK: state.common.base.kuSDK,
    currentModule: state.common.base.currentModule,
    currentSpace: state.common.base.currentSpace,
    currentGroup: state.common.case.currentGroup,
    docList: state.demand.doc.docList,
    currentDoc: state.demand.doc.currentDoc
}))(DocDetail);
