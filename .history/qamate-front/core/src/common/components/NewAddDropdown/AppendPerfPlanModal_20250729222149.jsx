import { forwardRef, useState, useCallback, useImperativeHandle } from 'react';
import { Popover, Form, InputNumber, Button, Space, message } from 'antd';
import EventBus from 'COMMON/utils/eventBus';
import baseModel from 'COMMON/models/baseModel';
import { getQueryParams } from 'COMMON/utils/utils';
import { connectModel } from 'COMMON/middleware';
import { appendPerfPlan } from 'COMMON/api/front_qe_tools/lazyperf';

function AppendPerfPlanModal(props, ref) {
    const { setShowModal } = props;
    const [open, setOpen] = useState(false);
    const [executeTimes, setExecuteTimes] = useState(2);
    const [loading, setLoading] = useState(false);
    const [addForm] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();
    const query = getQueryParams();

    const showModal = useCallback(() => {
        // 设置表单初始值
        setExecuteTimes(1);
        addForm.setFieldsValue({
            executeTimes: 1
        });
        setShowModal(true);
        setOpen(true);
    }, [addForm]);

    const hideModal = useCallback(() => {
        addForm.resetFields();
        setShowModal(false);
        setOpen(false);
    }, []);

    const onReset = () => {
        addForm.resetFields();
        hideModal();
    };

    const handleAppendExecute = () => {
        addForm
            ?.validateFields()
            .then(async (values) => {
                let body = {
                    planId: query?.planId,
                    appendExecuteTimes: values.executeTimes // 追加轮次数量
                };
                setLoading(true);
                appendPerfPlan(body)
                    .then(() => {
                        messageApi.success('追加任务成功');
                        EventBus.emit('refreshPerfPlanList');
                        hideModal();
                        setLoading(false);
                    })
                    .catch(() => {
                        setLoading(false);
                    });
            })
            .catch((err) => {
                console.log(err?.message ?? err);
                setLoading(false);
                messageApi.warning('请填写完整表单');
            });
    };

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(
        ref,
        () => {
            return {
                show: showModal
            };
        },
        [showModal]
    );

    return (
        <>
            {contextHolder}
            <Modal
                title="追加执行"
                autoComplete="off"
                open={open}
                destroyOnClose
                transitionName=""
                onCancel={hideModal}
                footer={
                    <div>
                        <Space>
                            <Button onClick={onReset}>取消</Button>
                            <Button type="primary" loading={loading} onClick={handleAppendExecute}>
                                确定
                            </Button>
                        </Space>
                    </div>
                }
                mask="true"
                maskClosable="false"
                width={window.innerWidth * 0.3}
            >
                <br />
                <Form form={addForm} colon={false} preserve={false} requiredMark={false}>
                    <Form.Item name="executeTimes" label="执行次数" initialValue={1}>
                        <div
                            style={{
                                display: 'flex',
                                alignContent: 'center',
                                alignItems: 'center',
                                gap: '8px'
                            }}
                        >
                            <InputNumber
                                min={1}
                                max={99}
                                style={{ width: '120px' }}
                                value={executeTimes}
                                onChange={(value) => {
                                    setExecuteTimes(value);
                                    addForm.setFieldsValue({ executeTimes: value });
                                }}
                                addonAfter="次"
                                placeholder="请输入执行次数"
                            />
                            <Button
                                onClick={() => {
                                    setExecuteTimes(10);
                                    addForm.setFieldsValue({ executeTimes: 10 });
                                }}
                            >
                                10次
                            </Button>
                            <Button
                                onClick={() => {
                                    setExecuteTimes(50);
                                    addForm.setFieldsValue({ executeTimes: 50 });
                                }}
                            >
                                50次
                            </Button>
                        </div>
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
}

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace
}))(forwardRef(AppendPerfPlanModal));
