import classnames from 'classnames';
import { forwardRef, useRef, useState, useEffect, useCallback, useImperativeHandle } from 'react';
import { message, Skeleton, Tooltip } from 'antd';
import { isEmpty } from 'lodash';
import { connectModel } from 'COMMON/middleware';
import { getBosToken } from 'COMMON/api/base/common';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import electron from 'COMMON/utils/electron';
import Loading from 'COMMON/components/common/Loading';
import StepOperator from './StepOperator';
import ImageActionv3 from './ImageActionv3';
import { editNativev3 } from './editNativev3';
import styles from './DomAction.module.less';
import DomProgress from './DomProgress';

function DomAction(props, ref) {
    const {
        descExtra,
        nodeId,
        editType,
        currentStep,
        curOsType,
        currentSpace,
        recordId,
        setRecordId,
        pageSourceSwitch,
        setCurrentStep,
        stepList,
        currentDevice,
        deviceList,
        handleUpdateStepList,
        handleUpdateStep,
        operationType
    } = props;

    const [findInfoType, setFindInfoType] = useState('widgetInfo');
    const [innerHeight, setInnerHeight] = useState(window.innerHeight);
    const [innerWidth, setInnerWidth] = useState(window.innerWidth);
    const [open, setOpen] = useState(false);
    const [errMsg, setErrMsg] = useState('');
    const [domShowProgress, setDomShowProgress] = useState(false);
    const [screenshotSlot, setScreenshotSlot] = useState(false);
    const [screenshotUrlSlot, setScreenshotUrlSlot] = useState(false);
    const [intactRecordSlot, setIntactRecordSlot] = useState(false);
    const [sketchyRecordSlot, setSketchyRecordSlot] = useState(false);
    const [domPercent, setDomPercent] = useState(0);
    const [imgSrc, setImgSrc] = useState('');
    const [loading, setLoading] = useState(true);
    const [messageApi, contextHolder] = message.useMessage();
    const [domDetail, setDomDetail] = useState({});
    const latestStepId = useRef(null);

    useImperativeHandle(
        ref,
        () => {
            return {
                start: recordUsualDomStep
            };
        },
        [recordUsualDomStep, recordId]
    );

    // dom 解析
    useEffect(() => {
        async function func() {
            try {
                let domInfo = currentStep?.stepInfo?.params?.recordInfo?.dom;
                // 解析url
                if (typeof domInfo === 'string' && domInfo?.startsWith('http')) {
                    let res = await fetch(domInfo).then((response) => response.json());
                    setDomDetail(res);
                } else {
                    setDomDetail(domInfo);
                }
            } catch (error) {
                console.log(error);
                message.error(error);
            }
        }
        func();
    }, [currentStep?.stepInfo?.params?.recordInfo?.dom, findInfoType]);

    // 页面大小变动后，图片resize
    useEffect(() => {
        window.onresize = () => {
            setInnerHeight(window.innerHeight);
            setInnerWidth(window.innerWidth);
        };
    });

    // 单击事件监听
    useEffect(() => {
        // setRecordId(null);
        setDomPercent(0);
        setDomShowProgress(false);
        setErrMsg('');
    }, [currentStep?.stepId]);

    useEffect(() => {
        let newImg = currentStep?.stepInfo?.params?.recordInfo?.deviceInfo?.screenshot;
        if (newImg?.startsWith('http') && -1 !== newImg.indexOf('https://hydra.bj.bcebos.com')) {
            setLoading(true);
            getBosToken({ bosLink: newImg }).then((res) => {
                if (!res?.token) {
                    message.error('身份校验失败，获取图片失败');
                }
                newImg += '?authorization=' + res.token;
                setImgSrc(newImg);
                setLoading(false);
            });
        } else {
            setImgSrc(newImg);
            setLoading(false);
        }
    }, [currentStep?.stepInfo?.params?.recordInfo?.deviceInfo?.screenshot, findInfoType]);

    // 单击事件监听
    useEffect(() => {
        window.addEventListener('click', clickAction);
        return () => window.removeEventListener('click', clickAction);
    }, [currentStep, domShowProgress, domDetail, findInfoType]);

    const editStep = async (step, domInfo) => {
        if (domInfo && domInfo.type === 'sketchyRecord' && isEmpty(domDetail)) {
            console.log('update sketchyRecord');
            step.stepInfo.params.recordInfo.dom = domInfo.dom;
            step.stepInfo.params.recordInfo.domInfo = domInfo.domInfo;
        }
        if (domInfo && domInfo.type === 'intactRecord') {
            step.stepInfo.params.recordInfo.dom = domInfo.dom;
            step.stepInfo.params.recordInfo.domInfo = domInfo.domInfo;
            console.log('update intactRecord');
        }
        if (!step.stepInfo?.params?.findParams?.scroll?.screenCount) {
            step.stepInfo.params.findParams.scroll = {
                screenCount: 1
            };
        }
        await handleUpdateStep(step);
    };

    const clickAction = (e) => {
        if (['readonly', 'debug', 'execute'].includes(editType) || isEmpty(domDetail)) {
            return false;
        }
        if (editType + 'StepImage' === e.target.className) {
            let ratio = window.devicePixelRatio || 1;
            editNativev3({
                step: currentStep,
                pageSourceSwitch,
                findInfoType,
                editType,
                domDetail,
                setCurrentStep,
                curOsType: curOsType,
                method: 'click',
                handleUpdateStep,
                xPrecent: (e.offsetX * ratio) / e.target.width,
                yPrecent: (e.offsetY * ratio) / e.target.height,
                ...props
            });
        }
    };

    useEffect(() => {
        if (recordId === null) {
            return;
        }
        let timer = null;
        let newPercent = 0;
        if (!domShowProgress) {
            newPercent = 0;
            clearInterval(timer);
            return;
        }
        timer = setInterval(() => {
            try {
                if (newPercent < 75) {
                    newPercent += 10;
                    setDomPercent(newPercent + 10);
                } else if (newPercent < 93) {
                    newPercent += 5;
                    setDomPercent(newPercent + 5);
                }
            } catch (error) {
                clearInterval(timer);
                setDomShowProgress(false);
                messageApi.error('建模失败，请重试');
                return false;
            }
            if (newPercent >= 93) {
                clearInterval(timer);
            }
        }, 1000);
        // 组件卸载时，清除定时器
        return () => clearInterval(timer);
    }, [recordId, domShowProgress]);

    const recordUsualDomStep = async () => {
        // 确定有设备连接
        let gotDevice = false;
        for (let device of deviceList[2 === +curOsType ? 'iOS' : 'android']) {
            if (device.deviceId === currentDevice?.deviceId) {
                gotDevice = true;
                if (2 !== device?.status) {
                    messageApi.error('请确保设备状态正常');
                    return;
                }
                break;
            }
        }
        if (!gotDevice) {
            messageApi.error('请确保有设备连接');
            return;
        }
        // 开启录制模式
        let { recordId } = await electron.send('device.record.dumpWidgetModel', {
            deviceType: 2 === +curOsType ? 'ios' : 'android',
            deviceId: currentDevice?.deviceId,
            nodeId: nodeId,
            stepId: +currentStep?.stepId,
            needUi: true,
            needPagesource: pageSourceSwitch,
            productId: currentSpace?.id,
            versionType: '3.0'
        });
        setRecordId(recordId);
        setErrMsg('');
        let step = {
            desc: currentStep?.stepInfo?.desc ?? '',
            common: currentStep?.stepInfo?.common
                ? { ...currentStep?.stepInfo?.common }
                : {
                      commonAlertClear: true
                  },
            type: 10,
            params: {}
        };
        step.params.findType = 0;

        let baseInfo_chosen_tag = currentStep?.stepInfo?.params?.findInfo?.baseInfo?.chosenTag ?? [
            'visual',
            'single'
        ];
        let widgetInfo_chosen_tag = currentStep?.stepInfo?.params?.findInfo?.widgetInfo
            ?.chosenTag ?? ['visual', 'single'];
        // 关闭系统建模配置，则去掉系统tag
        if (!pageSourceSwitch) {
            baseInfo_chosen_tag = baseInfo_chosen_tag.filter((item) => item !== 'system');
            widgetInfo_chosen_tag = widgetInfo_chosen_tag.filter((item) => item !== 'system');
        }
        // findInfo 参数
        step.params.findInfo = {
            baseInfo: {
                findType: 0,
                useFastSAM: false,
                chosenTag: baseInfo_chosen_tag,
                modelType: pageSourceSwitch ? 2 : 0,
                findNode: []
            },
            widgetInfo: {
                findType: 0,
                useFastSAM: false,
                chosenTag: widgetInfo_chosen_tag,
                modelType: pageSourceSwitch ? 2 : 0,
                findNode: []
            }
        };
        step.params.recordInfo = {};
        step.params.findParams = currentStep?.stepInfo?.params?.findParams;
        step.params.actionInfo = currentStep?.stepInfo?.params?.actionInfo;
        // editStep({...currentStep, stepInfo: step});
        setCurrentStep({ ...currentStep, stepInfo: step });
        // 开启录制模式
        setDomPercent(0);
        setDomShowProgress(true);
    };

    const changeUsualDomStep = async (findInfoType = 'widgetInfo', findType, modelType) => {
        let step = { ...currentStep.stepInfo };
        step.params.findInfo[findInfoType].findNode = [];
        let chosenTagEle = ['single'];
        if (findType === 0) {
            chosenTagEle = ['single'];
        }
        if (findType === 1) {
            chosenTagEle = ['multiple'];
        }
        let chosenTagMethod = ['visual'];
        if (modelType === 0) {
            chosenTagMethod = ['visual'];
        }
        if (modelType === 2) {
            chosenTagMethod = ['visual', 'system'];
        }
        step.params.findInfo[findInfoType].chosenTag = [...chosenTagEle, ...chosenTagMethod];
        step.params.actionInfo = currentStep.stepInfo?.params?.actionInfo;
        currentStep.stepInfo = step;
        editStep({ ...currentStep, stepInfo: step });
    };

    useEffect(() => {
        if (intactRecordSlot) {
            setSketchyRecordSlot(true);
        }
        if (screenshotSlot && screenshotUrlSlot && intactRecordSlot && sketchyRecordSlot) {
            console.log('finish');
            setDomShowProgress(false);
            setDomPercent(0);
            setIntactRecordSlot(false);
            setScreenshotSlot(false);
            setScreenshotUrlSlot(false);
            setSketchyRecordSlot(false);
            setRecordId(null);
        }
    }, [screenshotSlot, screenshotUrlSlot, intactRecordSlot, sketchyRecordSlot]);

    useEffect(() => {
        try {
            if (isElectron()) {
                electron.on('device.asyncRecord', async (res) => {
                    let { slotName, slotInfo } = res;
                    let data = slotInfo;
                    if (
                        data.recordId === recordId &&
                        Math.abs(data.stepId) === currentStep.stepId
                    ) {
                        if (slotName === 'error') {
                            setDomShowProgress(false);
                            setDomPercent(0);
                            setErrMsg(data.msg);
                            messageApi.error(data.msg);
                            return false;
                        } else if (domShowProgress !== true) {
                            setDomShowProgress(true);
                        }
                        console.log('slotName', slotName);
                        let newStep = { ...currentStep };
                        let { screenshot, screenshotUrl, widgetInfo, screenSize } = data;

                        if (slotName === 'screenshot') {
                            newStep.stepInfo.params.recordInfo.deviceInfo = {
                                type: 2 === curOsType ? 'ios' : 'android',
                                screenSize: {
                                    width: screenSize?.width,
                                    height: screenSize?.height,
                                    scale: screenSize?.scale,
                                    rotation: screenSize?.rotation
                                },
                                screenshot: screenshot
                            };
                            setCurrentStep(newStep);
                            setScreenshotSlot(true);
                        }
                        if (slotName === 'screenshotUrl') {
                            newStep.stepInfo.params.recordInfo.deviceInfo = {
                                ...newStep.stepInfo.params.recordInfo.deviceInfo,
                                screenshot: screenshotUrl
                            };
                            if (operationType !== 'ifThen') {
                                let newStepList = [...stepList];
                                let hasStep = false;
                                let stepIndex = stepList.findIndex(
                                    (item) => item.stepId === newStep.stepId
                                );
                                if (stepIndex !== -1) {
                                    hasStep = true;
                                    newStepList.splice(stepIndex, 1, newStep);
                                }
                                if (!hasStep) {
                                    newStepList = [];
                                    for (let _step of stepList) {
                                        let _copyStep = { ..._step };
                                        if (_copyStep.stepType === 1401) {
                                            let _child = [];
                                            _copyStep.stepChildren.forEach((_stepChild) => {
                                                if (_stepChild.stepId === newStep.stepId) {
                                                    _child.push(newStep);
                                                } else {
                                                    _child.push(_stepChild);
                                                }
                                            });
                                            _copyStep.stepChildren = _child;
                                            _copyStep.stepInfo.params.params.stepIdList =
                                                _child.map((item) => item.stepId);
                                        }
                                        newStepList.push(_copyStep);
                                    }
                                }
                                handleUpdateStepList(newStepList, newStep);
                            }
                            // setCurrentStep({...newStep});
                            setScreenshotUrlSlot(true);
                        }
                        if (slotName === 'sketchyRecord') {
                            if (widgetInfo) {
                                let { dom, domInfo } = widgetInfo;
                                if (isEmpty(dom)) {
                                    messageApi.error('未识别到有效端控件');
                                    setDomShowProgress(false);
                                    setDomPercent(0);
                                    return false;
                                }
                                let domUrl = await electron.send('file.uploadDomToBos', {
                                    dom: JSON.stringify(dom),
                                    nodeId: nodeId ?? -1,
                                    stepId: -1
                                });
                                setDomDetail(dom);
                                setSketchyRecordSlot(true);
                                editStep(newStep, {
                                    type: 'sketchyRecord',
                                    dom: domUrl,
                                    domInfo: domInfo
                                });
                            }
                        }
                        if (slotName === 'intactRecord') {
                            if (widgetInfo) {
                                let { dom, domInfo } = widgetInfo;
                                if (isEmpty(dom)) {
                                    messageApi.error('未识别到有效端控件');
                                    setDomShowProgress(false);
                                    setDomPercent(0);
                                    return false;
                                }
                                let domUrl = await electron.send('file.uploadDomToBos', {
                                    dom: JSON.stringify(dom),
                                    nodeId: nodeId ?? -1,
                                    stepId: -1
                                });
                                setDomDetail(dom);
                                setIntactRecordSlot(true);
                                editStep(newStep, {
                                    type: 'intactRecord',
                                    dom: domUrl,
                                    domInfo: domInfo
                                });
                            }
                        }
                    }
                });
                return () => electron.remove('device.asyncRecord');
            }
        } catch (err) {
            console.log(err?.message ?? err);
        }
    }, [currentStep, recordId]);

    if (loading) {
        return <Loading />;
    }

    return (
        <div className={styles.action}>
            {contextHolder}
            {descExtra}
            {-1 === ['debug', 'execute'].indexOf(editType) ? (
                <StepOperator
                    {...props}
                    findInfoType={findInfoType}
                    setFindInfoType={setFindInfoType}
                    imgSrc={imgSrc}
                    domDetail={domDetail}
                    innerHeight={innerHeight}
                    innerWidth={innerWidth}
                    setInnerHeight={setInnerHeight}
                    setInnerWidth={setInnerWidth}
                    recordUsualDomStep={recordUsualDomStep}
                    changeUsualDomStep={changeUsualDomStep}
                    open={open}
                    setOpen={setOpen}
                    operaOptions={{
                        showActionType: true,
                        showFindTypeOpera: true,
                        showModelTypeOpera: true,
                        showSettingOpera: true
                    }}
                    operationType={operationType}
                />
            ) : null}
            <Skeleton
                loading={!currentStep?.stepInfo?.params?.recordInfo?.deviceInfo?.screenSize}
                active
                style={{ padding: 50 }}
                paragraph={{ rows: 14 }}
            >
                <div
                    className={classnames(
                        styles.defaultAction,
                        { [styles.domActionWithExecute]: -1 !== ['execute'].indexOf(editType) },
                        { [styles.domAction]: -1 === ['debug', 'execute'].indexOf(editType) }
                    )}
                    style={{
                        height:
                            domShowProgress || errMsg !== ''
                                ? 'calc(100% - 90px)'
                                : 'calc(100% - 45px)'
                    }}
                >
                    <ImageActionv3
                        {...props}
                        findInfoType={findInfoType}
                        setFindInfoType={setFindInfoType}
                        domDetail={domDetail}
                        imgSrc={imgSrc}
                        innerHeight={innerHeight}
                        innerWidth={innerWidth}
                        setInnerHeight={setInnerHeight}
                        setInnerWidth={setInnerWidth}
                        recordUsualDomStep={recordUsualDomStep}
                        changeUsualDomStep={changeUsualDomStep}
                    />
                </div>
            </Skeleton>
            {domShowProgress || errMsg !== '' ? (
                <div className={styles.domCreateStatus}>
                    {operationType !== 'ifThen' && domShowProgress && (
                        <DomProgress percent={domPercent} />
                    )}
                    {errMsg !== '' && (
                        <div className={styles.domErrMsg}>
                            <Tooltip title={errMsg} placement="left">
                                <span>报错信息:&nbsp;{errMsg}</span>
                            </Tooltip>
                        </div>
                    )}
                </div>
            ) : null}
        </div>
    );
}

export default connectModel([commonModel, baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice,
    recordId: state.common.case.recordId,
    pageSourceSwitch: state.common.case.pageSourceSwitch
}))(forwardRef(DomAction));
