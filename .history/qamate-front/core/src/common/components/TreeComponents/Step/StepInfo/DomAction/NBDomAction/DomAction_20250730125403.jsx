import classnames from 'classnames';
import { useEffect, useState } from 'react';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import Loading from 'COMMON/components/common/Loading';
import { getBosToken } from 'COMMON/api/base/common';
import StepOperator from './StepOperator';
import ImageAction from './ImageAction';
import ImageActionv2 from './ImageActionv2';
import ImageActionv3 from './ImageActionv3';
import { editNative } from './editNative';
import { editNativev2 } from './editNativev2';
import { editNativev3 } from './editNativev3';
import styles from './DomAction.module.less';
import { message } from 'antd';

function DomAction(props) {
    const { descExtra, editType, currentStep, setCurrentStep, curOsType, handleUpdateStep } = props;
    const [innerHeight, setInnerHeight] = useState(window.innerHeight);
    const [innerWidth, setInnerWidth] = useState(window.innerWidth);
    const [loading, setLoading] = useState(false);
    const [imgSrc, setImgSrc] = useState('');

    // dom 解析
    const [domDetail, setDomDetail] = useState({});
    useEffect(() => {
        async function func() {
            try {
                let domInfo = currentStep?.stepInfo?.params?.dom;
                // 解析url
                if (typeof domInfo === 'string' && domInfo && domInfo.startsWith('http')) {
                    let res = await fetch(domInfo).then((response) => response.json());
                    setDomDetail(res);
                } else {
                    setDomDetail(domInfo);
                }
            } catch (error) {
                console.log(error);
                message.error(error);
            }
        }
        func();
    }, [currentStep?.stepInfo?.params?.dom]);

    // 页面大小变动后，图片resize
    useEffect(() => {
        window.onresize = () => {
            setInnerHeight(window.innerHeight);
            setInnerWidth(window.innerWidth);
        };
    });
    // 单击事件监听
    useEffect(() => {
        window.addEventListener('click', clickAction);
        return () => window.removeEventListener('click', clickAction);
    }, [currentStep, domDetail]);

    useEffect(() => {
        let newImg = currentStep?.stepInfo?.params?.deviceInfo?.screenshot;
        if (newImg?.startsWith('http') && -1 !== newImg.indexOf('https://hydra.bj.bcebos.com')) {
            setLoading(true);
            getBosToken({ bosLink: newImg }).then((res) => {
                if (!res?.token) {
                    messageApi.error('身份校验失败，获取图片失败');
                }
                newImg += '?authorization=' + res.token;
                setImgSrc(newImg);
                setLoading(false);
            });
        } else {
            setImgSrc(newImg);
            setLoading(false);
        }
    }, [currentStep?.stepInfo.params?.deviceInfo?.screenshot]);

    const clickAction = (e) => {
        if (['readonly', 'debug', 'execute'].includes(editType)) {
            return false;
        }
        if (editType + 'StepImage' === e.target.className) {
            let ratio = window.devicePixelRatio || 1;
            if (currentStep?.stepInfo.type === 5) {
                editNative({
                    step: currentStep,
                    editType,
                    domDetail,
                    curOsType: curOsType,
                    method: 'click',
                    handleUpdateStep,
                    xPrecent: (e.offsetX * ratio) / e.target.width,
                    yPrecent: (e.offsetY * ratio) / e.target.height,
                    ...props
                });
            }
            if (currentStep?.stepInfo.type === 6) {
                editNativev2({
                    step: currentStep,
                    editType,
                    domDetail,
                    curOsType: curOsType,
                    method: 'click',
                    handleUpdateStep,
                    xPrecent: (e.offsetX * ratio) / e.target.width,
                    yPrecent: (e.offsetY * ratio) / e.target.height,
                    ...props
                });
            }
            if ([7, 8].includes(currentStep?.stepInfo?.type)) {
                editNativev3({
                    step: currentStep,
                    editType,
                    domDetail,
                    setCurrentStep: setCurrentStep,
                    curOsType: curOsType,
                    method: 'click',
                    handleUpdateStep,
                    xPrecent: (e.offsetX * ratio) / e.target.width,
                    yPrecent: (e.offsetY * ratio) / e.target.height,
                    ...props
                });
            }
        }
    };

    if (loading) {
        return <Loading />;
    }

    return (
        <div className={styles.action}>
            {-1 === ['debug', 'execute'].indexOf(editType) ? (
                <StepOperator
                    {...props}
                    domDetail={domDetail}
                    innerHeight={innerHeight}
                    innerWidth={innerWidth}
                    setInnerHeight={setInnerHeight}
                    setInnerWidth={setInnerWidth}
                    imgSrc={imgSrc}
                />
            ) : null}
            {descExtra}
            <div
                className={classnames(styles.defaultAction, {
                    [styles.domAction]: -1 === ['debug'].indexOf(editType)
                })}
            >
                {currentStep?.stepInfo.type === 5 && (
                    <ImageAction
                        {...props}
                        imgSrc={imgSrc}
                        domDetail={domDetail}
                        innerHeight={innerHeight}
                        innerWidth={innerWidth}
                        setInnerHeight={setInnerHeight}
                        setInnerWidth={setInnerWidth}
                    />
                )}
                {[6].includes(currentStep?.stepInfo.type) && (
                    <ImageActionv2
                        {...props}
                        imgSrc={imgSrc}
                        domDetail={domDetail}
                        innerHeight={innerHeight}
                        innerWidth={innerWidth}
                        setInnerHeight={setInnerHeight}
                        setInnerWidth={setInnerWidth}
                    />
                )}
                {[7, 8].includes(currentStep?.stepInfo.type) && (
                    <ImageActionv3
                        {...props}
                        imgSrc={imgSrc}
                        domDetail={domDetail}
                        innerHeight={innerHeight}
                        innerWidth={innerWidth}
                        setInnerHeight={setInnerHeight}
                        setInnerWidth={setInnerWidth}
                    />
                )}
            </div>
        </div>
    );
}

export default connectModel([commonModel, baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice,
    recordId: state.common.case.recordId,
    pageSourceSwitch: state.common.case.pageSourceSwitch
}))(DomAction);
