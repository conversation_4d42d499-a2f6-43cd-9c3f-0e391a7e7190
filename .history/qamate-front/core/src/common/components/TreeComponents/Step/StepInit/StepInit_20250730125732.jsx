import classnames from 'classnames';
import {
    BlockOutlined,
    AimOutlined,
    ApiOutlined,
    DesktopOutlined,
    HomeOutlined,
    UngroupOutlined,
    UserOutlined,
    BulbOutlined,
    EllipsisOutlined,
    RocketOutlined,
    RocketTwoTone,
    ClearOutlined
} from '@ant-design/icons';
import { Divider, Switch } from 'antd';
import { useLocation } from 'umi';
import { intelligentCheckWhitelist, checkAiLocateWhitelist } from 'COMMON/config/whiteList';
import { getQueryParams } from 'COMMON/utils/utils';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';

import AIModelIcon from 'RESOURCES/img/aiModel.png';
import InitDomStep from './InitMarsDomStep';
import InitSystemStep from './InitSystemStep';
import InitManualStep from './InitManualStep';
import InitScreenStep from './InitScreenStep';
import InitCopyStep from './InitCopyStep';
import InterfaceStep from './InitInterfaceStep';
import InitAssertStep from './InitAssertStep';
import InitAIStep from './InitAIStep';
import InitMoreStep from './InitMoreStep';
import styles from './StepInit.module.less';

function StepInit(props) {
    const {
        curOsType,
        editType,
        type = null,
        defaultConfig,
        caseConfig,
        className,
        currentGroup,
        currentSpace,
        pageSourceSwitch,
        setPageSourceSwitch,
        spaceList
    } = props;
    const location = useLocation();

    let common_params = {
        commonAlertClear: defaultConfig?.mobileConfig?.stepConfig?.commonAlertClear ?? false,
        stepInterval: defaultConfig?.mobileConfig?.stepConfig?.stepInterval
            ? +defaultConfig?.mobileConfig?.stepConfig?.stepInterval
            : 2
    };
    let widget_params = {
        beforeReplayWait: defaultConfig?.mobileConfig?.widgetConfig?.beforeReplayWait
            ? +defaultConfig?.mobileConfig?.widgetConfig?.beforeReplayWait
            : 0,
        beforeActionWait: defaultConfig?.mobileConfig?.widgetConfig?.beforeActionWait
            ? +defaultConfig?.mobileConfig?.widgetConfig?.beforeActionWait
            : 0,
        screenCount: defaultConfig?.mobileConfig?.widgetConfig?.screenCount
            ? +defaultConfig?.mobileConfig?.widgetConfig?.screenCount
            : 1,
        retryTimes: defaultConfig?.mobileConfig?.stepConfig?.stepInterval
            ? +defaultConfig?.mobileConfig?.stepConfig?.stepInterval
            : 0
    };
    const query = getQueryParams();

    const changeSwitch = () => {
        setPageSourceSwitch(!pageSourceSwitch);
        localStorage.setItem('pageSourceSwitch', !pageSourceSwitch);
    };

    const IS_ELECTRON = !isElectron();

    const items = [
        {
            key: 'type_1',
            index: 1,
            filter: ![1, 2].includes(curOsType),
            disabled:
                ['execute', 'readonly'].includes(editType) ||
                IS_ELECTRON ||
                [4].includes(curOsType),
            label: (
                <span className={styles.stepInitName}>
                    <HomeOutlined className={styles.icon} />
                    系统
                </span>
            ),
            children: [
                {
                    key: 'step_1_1',
                    disabled: IS_ELECTRON || ![1, 2].includes(curOsType),
                    label: (
                        <InitSystemStep
                            {...props}
                            commonParams={common_params}
                            widgetParams={widget_params}
                            type="launchApp"
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    开启APP
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_1_2',
                    disabled: IS_ELECTRON || ![1, 2].includes(curOsType),
                    label: (
                        <InitSystemStep
                            {...props}
                            type="scheme"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    Scheme
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_1_3',
                    disabled: IS_ELECTRON || ![1, 2].includes(curOsType),
                    label: (
                        <InitSystemStep
                            {...props}
                            type="wait"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    休眠等待
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_1_4',
                    disabled:
                        ('template' === editType && type === 0 && IS_ELECTRON) ||
                        ![1, 2].includes(curOsType),
                    label: (
                        <InitSystemStep
                            {...props}
                            type="clearPop"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    弹窗点除
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_1_5',
                    disabled: 'template' === editType,
                    label: (
                        <InitSystemStep
                            {...props}
                            type="runTemplate"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    测试片段
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_1_6',
                    disabled: (curOsType === 2 && IS_ELECTRON) || ![1].includes(curOsType),
                    filter: ![1].includes(curOsType),
                    label: (
                        <InitSystemStep
                            {...props}
                            type="clearApp"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    清理APP
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_1_7',
                    disabled: (curOsType !== 1 && IS_ELECTRON) || ![1].includes(curOsType),
                    filter: ![1].includes(curOsType),
                    label: (
                        <InitSystemStep
                            {...props}
                            type="authApp"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    授权APP
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_1_8',
                    disabled: IS_ELECTRON || ![1, 2].includes(curOsType),
                    label: (
                        <InitSystemStep
                            {...props}
                            commonParams={common_params}
                            widgetParams={widget_params}
                            type="back"
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    返回上一页
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_1_9',
                    disabled: curOsType !== 1 && (IS_ELECTRON || ![1].includes(curOsType)),
                    filter: ![1].includes(curOsType),
                    label: (
                        <InitSystemStep
                            {...props}
                            commonParams={common_params}
                            widgetParams={widget_params}
                            type="pushFile"
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    推送文件
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_1_10',
                    disabled: curOsType !== 1 || currentGroup?.groupType !== 4,
                    label: (
                        <InitSystemStep
                            {...props}
                            commonParams={common_params}
                            widgetParams={widget_params}
                            type="shell"
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    Shell
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_1_11',
                    disabled: IS_ELECTRON || ![1, 2].includes(curOsType),
                    label: (
                        <InitSystemStep
                            {...props}
                            commonParams={common_params}
                            widgetParams={widget_params}
                            type="closeApp"
                            extra={
                                <>
                                    <ClearOutlined className={styles.icon} />
                                    关闭APP
                                </>
                            }
                        />
                    )
                },
            ]
        },
        {
            key: 'type_2',
            index: 2,
            filter: ![1, 2].includes(curOsType),
            disabled: IS_ELECTRON || ['execute', 'readonly'].includes(editType),
            label: (
                <InitDomStep
                    {...props}
                    type="tap"
                    commonParams={common_params}
                    widgetParams={widget_params}
                    extra={
                        <>
                            <AimOutlined className={styles.icon} />
                            控件
                        </>
                    }
                />
            ),
            children: [
                {
                    key: 'step_2_1',
                    label: (
                        <span>
                            <Switch
                                size="small"
                                checked={pageSourceSwitch}
                                onClick={() => changeSwitch()}
                            />
                            &nbsp;建模系统控件1
                        </span>
                    )
                }
            ]
        },
        {
            key: 'type_4',
            index: 4,
            filter: ![1, 2].includes(curOsType),
            disabled: IS_ELECTRON || ['execute', 'readonly'].includes(editType),
            label: (
                <span className={styles.stepInitName}>
                    <DesktopOutlined className={styles.icon} />
                    屏幕
                </span>
            ),
            children: [
                {
                    key: 'step_4_1',
                    label: (
                        <InitSystemStep
                            {...props}
                            type="home"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    返回主屏幕
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_4_2',
                    label: (
                        <InitSystemStep
                            {...props}
                            commonParams={common_params}
                            widgetParams={widget_params}
                            type="swipe"
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    Swipe
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_4_3',
                    disabled: 'template' === editType,
                    label: (
                        <InitScreenStep
                            {...props}
                            commonParams={common_params}
                            widgetParams={widget_params}
                            type="tap"
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    屏幕点击
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_4_4',
                    disabled: 'template' === editType,
                    label: (
                        <InitScreenStep
                            {...props}
                            type="swipe"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    屏幕划动
                                </>
                            }
                        />
                    )
                }
            ]
        },
        {
            key: 'type_5',
            index: 5,
            filter: ![1, 2].includes(curOsType),
            disabled: IS_ELECTRON || ['execute', 'readonly'].includes(editType),
            label: (
                <span className={styles.stepInitName}>
                    <ApiOutlined className={styles.icon} />
                    代理
                </span>
            ),
            children: [
                {
                    key: 'step_5_1',
                    label: (
                        <InitSystemStep
                            {...props}
                            commonParams={common_params}
                            widgetParams={widget_params}
                            type="mock"
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    数据模拟
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_5_2',
                    label: (
                        <InitSystemStep
                            {...props}
                            type="requestVerify"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    请求校验
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_5_3',
                    label: (
                        <InitSystemStep
                            {...props}
                            type="request"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    请求调用
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_5_4',
                    label: (
                        <InitSystemStep
                            {...props}
                            type="requestRedirect"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    请求转发
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_5_5',
                    label: (
                        <InitSystemStep
                            {...props}
                            type="connect"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    网络连接
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_5_6',
                    label: (
                        <InitSystemStep
                            {...props}
                            type="disconnect"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    断网模拟
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_5_7',
                    label: (
                        <InitSystemStep
                            {...props}
                            type="clearRequest"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    请求清空
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_5_8',
                    label: (
                        <InitSystemStep
                            {...props}
                            type="clearMock"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    Mock清空
                                </>
                            }
                        />
                    )
                }
            ]
        },
        {
            key: 'type_6',
            index: 6,
            disabled: 'template' === editType || ['execute', 'readonly'].includes(editType),
            filter: curOsType === 4,
            label: (
                <span className={styles.stepInitName}>
                    <UserOutlined className={styles.icon} />
                    人工
                </span>
            ),
            children: [
                {
                    key: 'step_6_1',
                    label: (
                        <InitManualStep
                            {...props}
                            type="operator"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    人工操作
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_6_2',
                    label: (
                        <InitManualStep
                            {...props}
                            type="assest"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    人工复验
                                </>
                            }
                        />
                    )
                }
            ]
        },
        {
            key: 'type_9',
            index: 9,
            filter: ![1, 2].includes(curOsType),
            disabled:
                ('template' === editType && type === 0) ||
                IS_ELECTRON ||
                ['execute', 'readonly'].includes(editType),
            label: (
                <span className={styles.stepInitName}>
                    <BlockOutlined className={styles.icon} />
                    断言
                </span>
            ),
            children: [
                {
                    key: 'step_7_1',
                    // feed核心、文心智能体、小说客户端、内核
                    // [2, 9, 85, 231]
                    disabled: !intelligentCheckWhitelist(currentSpace, spaceList),
                    label: (
                        <InitAssertStep
                            {...props}
                            type="aiAssert"
                            disabled={[]}
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <span className={styles.aiStep}>
                                    <span className={styles.imgIcon}>
                                        <img src={AIModelIcon} width={14} height={14} />
                                    </span>
                                    智能校验
                                </span>
                            }
                        />
                    )
                },
                {
                    key: 'step_7_2',
                    label: (
                        <InitSystemStep
                            {...props}
                            type="whiteScreen"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    白屏检测
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_7_3',
                    disabled: 'template' === editType || IS_ELECTRON,
                    label: (
                        <InitSystemStep
                            {...props}
                            type="logCheck"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <>
                                    <UngroupOutlined className={styles.icon} />
                                    点位校验
                                </>
                            }
                        />
                    )
                }
            ]
        },
        {
            key: 'type_7',
            index: 7,
            filter: ![3].includes(caseConfig?.osType) || curOsType === 4,
            disabled: ['execute', 'template', 'readonly'].includes(editType) || IS_ELECTRON,
            label: (
                <span className={styles.stepInitName}>
                    <BulbOutlined className={styles.icon} />
                    生成
                </span>
            ),
            children: [
                {
                    key: 'step_7_1',
                    label: (
                        <InitCopyStep
                            {...props}
                            type="operator"
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    步骤拷贝
                                </>
                            }
                        />
                    )
                }
            ]
        },
        {
            key: 'type_10',
            index: 10,
            filter: curOsType === 4 || editType !== 'template',
            disabled: ['execute', 'edit', 'readonly'].includes(editType) || IS_ELECTRON,
            label: (
                <span className={styles.stepInitName}>
                    <EllipsisOutlined className={styles.icon} />
                    更多
                </span>
            ),
            children: [
                {
                    key: 'step_10_1',
                    label: (
                        <InitMoreStep
                            {...props}
                            type="installApp"
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    安装 APP
                                </>
                            }
                        />
                    )
                },
                {
                    key: 'step_10_2',
                    label: (
                        <InitMoreStep
                            {...props}
                            type="login"
                            extra={
                                <>
                                    <BlockOutlined className={styles.icon} />
                                    登录账号
                                </>
                            }
                        />
                    )
                }
            ]
        },
        {
            key: 'type_11',
            index: 11,
            disabled: ['execute', 'readonly'].includes(editType),
            filter: curOsType !== 4,
            label: (
                <InterfaceStep
                    {...props}
                    type="http"
                    commonParams={common_params}
                    widgetParams={widget_params}
                    extra={<div className={styles.apiConent}>HTTP</div>}
                />
            )
        },
        {
            key: 'type_12',
            index: 12,
            disabled: ['execute', 'readonly'].includes(editType),
            filter: true,
            itemStyle: {
                width: 100
            },
            label: (
                <InterfaceStep
                    {...props}
                    type="stargateRPC"
                    commonParams={common_params}
                    widgetParams={widget_params}
                    extra={<div className={styles.apiConent}>StargateRPC</div>}
                />
            )
        },
        {
            key: 'type_12',
            index: 12,
            disabled: ['execute', 'readonly'].includes(editType),
            filter: curOsType !== 4,
            label: (
                <InterfaceStep
                    {...props}
                    type="sql"
                    commonParams={common_params}
                    widgetParams={widget_params}
                    extra={<div className={styles.apiConent}>SQL</div>}
                />
            )
        },
        {
            key: 'type_13',
            index: 13,
            disabled: ['execute', 'readonly'].includes(editType),
            filter: curOsType !== 4,
            label: (
                <InterfaceStep
                    {...props}
                    type="redis"
                    commonParams={common_params}
                    widgetParams={widget_params}
                    extra={<div className={styles.apiConent}>Redis</div>}
                />
            )
        },
        {
            key: 'step_1_5',
            disabled: 'template' === editType || ['execute', 'readonly'].includes(editType),
            filter: curOsType !== 4,
            itemStyle: {
                width: 80
            },
            label: (
                <InitSystemStep
                    {...props}
                    type="runTemplate"
                    commonParams={common_params}
                    widgetParams={widget_params}
                    extra={<div className={styles.apiConent}> 测试片段</div>}
                />
            )
        },
        {
            key: 'ai',
            disabled: ['execute', 'readonly'].includes(editType) || IS_ELECTRON,
            filter: !checkAiLocateWhitelist(currentSpace, spaceList) || ![1, 2].includes(curOsType),
            label: (
                <span
                    className={classnames(styles.stepInitName, {
                        [styles.stepInitNameWithAI]: !(
                            ['execute', 'readonly'].includes(editType) || IS_ELECTRON
                        )
                    })}
                >
                    {!(['execute', 'readonly'].includes(editType) || IS_ELECTRON) ? (
                        <RocketTwoTone
                            className={styles.icon}
                            twoToneColor="rgba(65, 69, 216, 0.7)"
                        />
                    ) : (
                        <RocketOutlined className={styles.icon} />
                    )}
                    智能
                </span>
            ),
            children: [
                {
                    key: 'aiLocate',
                    label: (
                        <InitAIStep
                            {...props}
                            type="aiLocate"
                            commonParams={common_params}
                            widgetParams={widget_params}
                            extra={
                                <span className={styles.aiStep}>
                                    <span className={styles.imgIcon}>
                                        <img src={AIModelIcon} width={14} height={14} />
                                    </span>
                                    智能定位
                                </span>
                            }
                        />
                    )
                }
            ]
        }
    ];

    return (
        <div className={classnames(styles.layoutSider, className)}>
            <div
                className={classnames({
                    [styles.stepInitList]:
                        editType === 'edit' &&
                        localStorage.getItem('common_view') === 'vertical' &&
                        !location.pathname.includes('/intelligent') &&
                        !location.pathname.includes('/case/stamp')
                })}
            >
                {items
                    ?.filter((item) => !item?.filter)
                    .map((item) => {
                        return (
                            <div
                                key={`step_init_${item.index}`}
                                className={classnames(
                                    styles.stepInitItem,
                                    { [styles.stepInitAllowItem]: !item?.disabled },
                                    { [styles.stepInitNotAllowItem]: item?.disabled }
                                )}
                                style={{
                                    cursor: item?.disabled ? 'not-allowed' : 'cursor',
                                    ...(item?.itemStyle || {})
                                }}
                            >
                                {item.label}
                                <div className={styles.stepsList}>
                                    {item.children ? (
                                        <div
                                            className={classnames(
                                                { [styles.stepInfoSteps]: item.key === 'type_2' },
                                                { [styles.stepInitSteps]: item.key !== 'type_2' }
                                            )}
                                        >
                                            {item.children?.map((child, index) => {
                                                if (!child.disabled) {
                                                    return (
                                                        <span
                                                            key={`step_init_${item.index}_${String(
                                                                index
                                                            )}`}
                                                            className={classnames(
                                                                {
                                                                    [styles.stepInfo]:
                                                                        item.key === 'type_2'
                                                                },
                                                                {
                                                                    [styles.stepInitStep]:
                                                                        item.key !== 'type_2'
                                                                }
                                                            )}
                                                        >
                                                            {child.label}
                                                        </span>
                                                    );
                                                }
                                            })}
                                        </div>
                                    ) : null}
                                </div>
                            </div>
                        );
                    })}
            </div>
        </div>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    defaultConfig: state.common.base.defaultConfig,
    pageSourceSwitch: state.common.case.pageSourceSwitch,
    caseConfig: state.common.case.caseConfig,
    currentGroup: state.common.case.currentGroup,
    spaceList: state.common.base.spaceList
}))(StepInit);
