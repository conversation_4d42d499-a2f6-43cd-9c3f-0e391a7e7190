import { message } from 'antd';
import zlib from 'zlib';
import { updateToBos } from 'COMMON/api/base/common';

const defaultFieldNames = {
    label: 'label', // 树节点的label
    value: 'value', // 树节点的value
    children: 'children', //
    parentKey: 'parent_id',
    rootKeyValue: -1,
    key: 'id'
};

// 防止连续点击
let clickTimer = 0;
export function clickThrottle(interval = 1000) {
    let now = +new Date(); // 获取当前时间的时间戳
    let timer = clickTimer; // 记录触发事件的事件戳
    if (now - timer < interval) {
        return false;
    } else {
        clickTimer = now;
        return true;
    }
}

// 内容上传bos
export const uploadContentToBos = async (content, type = 'knowledge', isHtml = true) => {
    return await new Promise(async (resolve, reject) => {
        zlib.gzip(
            JSON.stringify({
                content: !isHtml ? content : html2Escape(content),
                suffix: type
            }),
            async (error, zipBuf) => {
                if (error) {
                    reject(error);
                    return;
                }
                try {
                    let bosUrl = await updateToBos(zipBuf, {
                        headers: {
                            'Content-Type': 'application/json',
                            'Content-Encoding': 'gzip'
                        }
                    });
                    resolve({
                        fileId: 1,
                        url: bosUrl
                    });
                } catch (err) {
                    reject(err);
                }
            }
        );
    });
};

// base64 转 File 类型
export function base64ToFile(base64String, fileName) {
    const arr = base64String.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);

    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }

    return new File([u8arr], fileName, { type: mime });
}

// file 转base64
export function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            resolve(reader.result);
        };
        reader.onerror = () => {
            reject(new Error('Failed to load file'));
        };
    });
}

// base64 图片上传
export async function base64ImageUpload(data) {
    return await new Promise(async (resolve, reject) => {
        zlib.gzip(
            JSON.stringify({
                content: data,
                suffix: 'png'
            }),
            async (error, zipBuf) => {
                if (error) {
                    reject(error);
                    return;
                }
                try {
                    let bosUrl = await updateToBos(zipBuf, {
                        headers: {
                            'Content-Type': 'application/json',
                            'Content-Encoding': 'gzip'
                        }
                    });
                    resolve({
                        fileId: 1,
                        url: bosUrl
                    });
                } catch (err) {
                    reject(err);
                }
            }
        );
    });
}

// 文件 =》 图片上传
export async function fileImageUpload(data) {
    return await new Promise(async (resolve, reject) => {
        // 将文件转换为Base64编码
        const base64 = await fileToBase64(data);
        zlib.gzip(
            JSON.stringify({
                content: base64,
                suffix: data.name.split('.')[1]
            }),
            async (error, zipBuf) => {
                if (error) {
                    reject(error);
                    return;
                }
                try {
                    let bosUrl = await updateToBos(zipBuf, {
                        headers: {
                            'Content-Type': 'application/json',
                            'Content-Encoding': 'gzip'
                        }
                    });
                    resolve({
                        fileId: 1,
                        url: bosUrl
                    });
                } catch (err) {
                    reject(err);
                }
            }
        );
    });
}

export function escape2Html(str) {
    let dom = document.createElement('div');
    dom.innerHTML = str;
    let output = dom.innerText || dom.textContent;
    return output;
}

export function html2Escape(sHtml) {
    return sHtml?.replace(/[<>&"]/g, function (c) {
        return { '<': '&lt;', '>': '&gt;', '&': '&amp;', '"': '&quot;' }[c];
    });
}

export const getFiexd = (data) => {
    return Math.floor(data * 10000) / 100;
};

export const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

/**
 * 将扁平数组转换为树状数组
 * @param arr
 * @param fieldNames
 * @returns {[]|Array}
 */
export function convertTreeData(arr, fieldNames = defaultFieldNames) {
    if (!Array.isArray(arr)) {
        return [];
    }
    const { label, value, parentKey, rootKeyValue } = { ...defaultFieldNames, ...fieldNames };
    const result = [];
    const roots = arr.filter((item) => item[parentKey] === rootKeyValue);
    for (let i = 0; i < roots.length; i++) {
        let data = { value: roots[i][value], label: roots[i][label], node: roots[i] };
        result.push(filterChildrenNode(data, arr, { ...defaultFieldNames, ...fieldNames }));
    }
    return result;
}

export function filterChildrenNode(parent, arr, fieldNames) {
    const { label, value, children, parentKey, key } = fieldNames;
    const _children = arr.filter((item) => item[parentKey] === parent.node[key]);
    if (_children.length > 0) {
        parent[children] = [];
        for (let i = 0; i < _children.length; i++) {
            const data = { value: _children[i][value], label: _children[i][label], node: _children[i] };
            parent[children].push(filterChildrenNode(data, arr, fieldNames));
        }
    }
    return parent;
}

export function getObjectURL(file) {
    let url = null;
    if (window.createObjectURL !== undefined) {
        // basic
        url = window.createObjectURL(file);
    } else if (window.URL !== undefined) {
        // mozilla(firefox)
        url = window.URL.createObjectURL(file);
    } else if (window.webkitURL !== undefined) {
        // webkit or chrome
        url = window.webkitURL.createObjectURL(file);
    }
    return url;
}

/**
 * 用来获取url中的参数，如果传了field返回指定的，如果没传参数，返回所有的。
 * @param field
 * @returns {{}|*}
 */
export function getQueryParams(field, path) {
    let url = path ?? document.location.hash;
    if (url === '') {
        url = document.location.href;
    }
    // 如果url中有特殊字符则需要进行一下解码
    url = decodeURI(url);
    const arr1 = url.split('?');
    const obj = {};
    if (arr1.length > 1) {
        const arr2 = arr1[1].split('&');
        for (let i = 0; i < arr2.length; i++) {
            const curArr = arr2[i].split('=');
            obj[curArr[0]] = decodeURIComponent(curArr[1]);
        }
    }
    if (field) {
        return obj[field];
    }
    return obj;
}

// 捕获await后面promise的异常
export const catchErrorFromPromiseUseAwait = (promise) => {
    return promise.then((data) => [null, data]).catch((error) => [error, null]);
};

export const copyText = (text) => {
    if (!text) {
        return;
    }
    const input = document.createElement('input');
    input.style.position = 'absolute';
    input.style.top = '-1000px';
    input.style.left = '-1000px';
    document.body.appendChild(input);
    input.value = text;
    input.select();
    document.execCommand('copy');
    message.success('复制成功');
    document.body.removeChild(input);
};

/**
 * 判断是否是灰色
 * @param color
 */
export const isGreyTag = (color) => {
    return color === '#fafafa';
};
export const getBusinessTypeInitialValue = (osType) => {
    if ([1, 2, 3].includes(+osType)) {
        return 0;
    }
    if (+osType === 4) {
        return 1;
    }
    if (+osType === 5) {
        return 2;
    }
    return 1;
};

export const getSignTypeInitialValueWith3 = (params) => {
    if (params?.caseConfig) {
        const { osType, signType } = params.caseConfig;
        if (osType === 3) {
            return signType === 0 ? 1 : 0;
        }
    }
    return 0;
};

export const getSignTypeInitialValue = (params) => {
    if (params?.caseConfig) {
        const { osType } = params.caseConfig;
        const { signType } = params.signConfig;
        if (osType === 1) {
            return 2;
        }
        if (osType === 2) {
            return 3;
        }
        if (osType === 3) {
            // caseConfig signType 0 共用 / 1 不共用
            // 对应form值 0 不共用 / 1共用
            return signType === 0 ? 1 : 0;
        }
    }
    return 1;
};

// 判断是否为json
export function isJSON(str) {
    if (typeof str === 'string') {
        try {
            let obj = JSON.parse(str);
            if (typeof obj === 'object' && obj) {
                return true;
            } else {
                return false;
            }
        } catch (e) {
            return false;
        }
    }
}

// query 更新
export function updateQueryParam(param, newVal) {
    var url = window.location.href;
    var re = new RegExp('([?&])' + param + '=([^&]*)');

    if (re.test(url)) {
        url = url.replace(re, '$1' + param + '=' + newVal);
    } else {
        url += (url.indexOf('?') === -1 ? '?' : '&') + param + '=' + newVal;
    }
    return url;
}

// query 删除
export function removeQueryParam(key) {
    // 获取当前URL
    var url = window.location.href;
    // 正则匹配查询参数并构造新的URL
    var r = new RegExp('(\\?|&)' + key + '=[^&]*(&|$)', 'gi');
    var newUrl = url.replace(r, function (match, p1, p2) {
        if (p1 === '?') {
            return p2 === '&' ? '?' : '';
        }
        return p2 === '&' ? '' : '';
    });
    return newUrl;
}

/**
 * JSON 格式化
 * @param {*} value
 * @returns
 */
export const safeJsonStringify = (value) => {
    try {
        // 格式化JSON，添加缩进
        return JSON.stringify(JSON.parse(value), null, 2);
    } catch (error) {
        // 转化失败，返回原值
        return value;
    }
};

/**
 * json 格式化
 * @param {*} value  需要格式化的json字符串
 * @param {*} defaultValue  转化失败默认值
 * @returns
 */
export const safeJsonParse = (value, defaultValue = {}) => {
    try {
        return JSON.parse(value);
    } catch (error) {
        return defaultValue || value;
    }
};

/**
 * 获取.json结尾的bos数据
 * @param {*} bosLink bos链接
 * @param {*} defaultValue  转化失败默认值
 * @returns
 */
export const getBoxLinkContent = async (bosLink) => {
    try {
        if (!bosLink || typeof bosLink !== 'string') {
            console.error('getBoxLinkContent: Invalid bosLink provided:', bosLink);
            return null;
        }
        const content = await fetch(bosLink, {
            cache: 'no-cache'
        }).then((res) => {
            return res.json();
        });
        return content;
    } catch (error) {
        console.log(error);
    }
};


export const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));