/**
 * @create 王家麒@2022.06.01
 */
const fs = require('fs');
const zlib = require('zlib');
const { v4: uuidv4 } = require('uuid');
const { time: { currentTimestamp, delay }, json: { jsonParse } } = require('@baidu/bat-util');

const { deepcopy } = require('../../../util/copy');
const { read, uploadFileAndDelete } = require('../../../util/file');
const { cloudServerRequest } = require('../../../util/request');
const { get: getAccount } = require('../../../util/account');
const { CACHE: { CACHE_FILE_DIR } } = require('../../../config');

const getLogTag = method => `[worker.task.run.qamate.${method}]`;

const UBC_DELAY_SECOND = 15;

class QAMateTask {
    constructor({ taskId, taskInfo, deviceType, deviceId, phoneId, worker }) {
        this.moduleId = taskInfo.moduleId;
        this.planId = taskInfo.planId;
        this.caseNodeId = taskInfo.caseNodeId;
        this.productId = taskInfo.productId;
        this.taskId = taskId;
        this.taskInfo = taskInfo;
        this.deviceType = deviceType;
        this.deviceId = deviceId;
        this.phoneId = phoneId;
        this.worker = worker;
        this.osType = 1;
        if (this.deviceType === 'iOS') {
            this.osType = 2;
        }

        this.account = getAccount();
        this.recordTag = undefined;
        this.caseList = [];
        this.paramsList = [];
        this.hideParamsList = [
            {
                name: 'ONE_USERNAME',
                value: this.account.userName,
                hide: true,
            },
            {
                name: 'ONE_PASSWORD',
                value: this.account.userPasswd,
                hide: true
            }
        ];
        this.staticFile = {};
        this.isFinish = false;
        this.sysAlertClear = undefined === taskInfo.sysAlertClear ? true : taskInfo.sysAlertClear;
        this.commonAlertClear = undefined === taskInfo.commonAlertClear ? false : taskInfo.commonAlertClear;
        this.ubcCollect = undefined === taskInfo.ubcCollect ? false : taskInfo.ubcCollect;

        this.deviceLogCat = false;
        this.deviceScreenRecord = false;
        this.deviceUbcCollect = false;

        this.resBody = {
            code: 0,
            message: 'success',
            result: {}
        };
    }

    async uploadStaticFile({ filePath, bosPath }) {
        if (undefined !== this.staticFile[filePath]) {
            return this.staticFile[filePath];
        }
        if (false === fs.existsSync(filePath)) {
            return '';
        }
        let remotePath = await uploadFileAndDelete({ filePath, bosPath });
        this.staticFile[filePath] = remotePath;
        return remotePath;
    }

    async openLogCat() {
        try {
            let { logCollect = {} } = this.taskInfo;
            let { needLog = false, filter = '' } = logCollect;
            if (false === needLog) {
                return;
            }
            this.deviceLogCat = true;
            await this.worker.send('device', 'runSingleStep', [{
                deviceType: this.deviceType,
                deviceId: this.deviceId,
                step: {
                    type: 1,
                    params: {
                        type: 'logcat',
                        params: {
                            filter
                        }
                    }
                },
                needDeviceInfo: false,
                checkStatus: false
            }], 600000);
            this.worker.logger.info(`${getLogTag('openLogCat')} logcat 已开启`);
        }
        catch (err) {
            this.worker.logger.warn(`${getLogTag('openLogCat')} 开启 logcat 失败 ${err.stack}`);
        }
    }

    async closeLogCat({ needReturn = true }) {
        if (false === this.deviceLogCat) {
            return;
        }
        try {
            let { extra: { logFile } } = await this.worker.send('device', 'runSingleStep', [{
                deviceType: this.deviceType,
                deviceId: this.deviceId,
                step: {
                    type: 1,
                    params: {
                        type: 'logcat',
                        params: {
                            needClose: true
                        }
                    }
                },
                needDeviceInfo: false,
                checkStatus: false
            }], 600000);
            this.deviceLogCat = false;
            if (!needReturn) {
                return;
            }
            this.worker.logger.info(`${getLogTag('closeLogCat')} logcat 本地目录 ${logFile}`);
            if (!fs.existsSync(logFile)) {
                return;
            }
            logFile = await this.uploadStaticFile({
                filePath: logFile,
                bosPath: `/lazycloud/${this.taskId}-${currentTimestamp()}-${uuidv4()}.log`
            });
            this.worker.logger.info(`${getLogTag('closeLogCat')} logcat 云端路径 ${logFile}`);
            this.caseList[0].step[0].result.data.extra.logFile = logFile;
        }
        catch (err) {
            this.worker.logger.warn(`${getLogTag('closeLogCat')} 关闭 logcat 失败 ${err.stack}`);
        }
    }

    async closeScreenRecord({ needReturn = true }) {
        if (false === this.deviceScreenRecord) {
            return;
        }
        try {
            let startTime = currentTimestamp();
            let { extra: { recordFile } } = await this.worker.send('device', 'runSingleStep', [{
                deviceType: this.deviceType,
                deviceId: this.deviceId,
                step: {
                    type: 1,
                    params: {
                        type: 'screenRecord',
                        params: {
                            needClose: true
                        }
                    }
                },
                needDeviceInfo: false,
                checkStatus: false
            }], 600000);
            this.deviceScreenRecord = false;
            if (!needReturn) {
                return;
            }
            this.worker.logger.info(`${getLogTag('closeScreenRecord')} screenRecord 本地文件 ${recordFile}`);
            if (!fs.existsSync(recordFile)) {
                return;
            }
            recordFile = await this.uploadStaticFile({
                filePath: recordFile,
                bosPath: `/lazycloud/${this.taskId}-${currentTimestamp()}-${uuidv4()}.mp4`
            });
            this.worker.logger.info(
                `${getLogTag('closeScreenRecord')} screenRecord 云端路径 ${recordFile}` +
                ` 整体耗时 ${currentTimestamp() - startTime}ms`
            );
            this.caseList[0].step[0].result.data.extra.recordFile = recordFile;
        }
        catch (err) {
            this.worker.logger.warn(`${getLogTag('closeScreenRecord')} 关闭 screenRecord 失败 ${err.stack}`);
        }
    }

    async openScreenRecord() {
        try {
            let { needScreenRecord = false } = this.taskInfo;
            if (false === needScreenRecord) {
                return;
            }
            this.deviceScreenRecord = true;
            await this.worker.send('device', 'runSingleStep', [{
                deviceType: this.deviceType,
                deviceId: this.deviceId,
                step: {
                    type: 1,
                    params: {
                        type: 'screenRecord',
                        params: {}
                    }
                },
                needDeviceInfo: false,
                checkStatus: false
            }], 600000);
            this.worker.logger.info(`${getLogTag('openScreenRecord')} screenRecord 已开启`);
        }
        catch (err) {
            this.worker.logger.warn(`${getLogTag('openScreenRecord')} 开启 screenRecord 失败 ${err.stack}`);
        }
    }

    async openUbcCollect() {
        try {
            // 重置采集器
            await this.worker.send(`proxy_${this.phoneId}`, 'stopUbcRecord', [false]);
            if (false === this.ubcCollect) {
                return;
            }
            // 开启采集器
            this.worker.logger.info(`${getLogTag('openUbcCollect')} 开启 UBC 收集`);
            await this.worker.send(`proxy_${this.phoneId}`, 'startUbcRecord');
            this.deviceUbcCollect = true;
        }
        catch (err) {
            this.worker.logger.warn(`${getLogTag('openUbcCollect')} 开启 UBC 收集失败 ${err.stack}`);
        }
    };

    async closeUbcCollect() {
        if (false === this.deviceUbcCollect) {
            return;
        }
        try {
            this.worker.logger.info(`${getLogTag('closeUbcCollect')} 关闭 UBC 收集延迟 ${UBC_DELAY_SECOND}s`);
            await delay(UBC_DELAY_SECOND * 1000);
            this.worker.logger.info(`${getLogTag('closeUbcCollect')} 关闭 UBC 收集`);
            let ubcList = await this.worker.send(`proxy_${this.phoneId}`, 'stopUbcRecord', [this.ubcCollect]);
            this.deviceUbcCollect = false;
            if (false === this.ubcCollect || 0 !== this.resBody.code || this.caseNodeId <= 0) {
                return;
            }
            let ubcFilePath = `${CACHE_FILE_DIR}/${this.taskId}-${currentTimestamp()}-${uuidv4()}.json`;
            fs.writeFileSync(ubcFilePath, JSON.stringify(ubcList));
            this.worker.logger.info(`${getLogTag('closeUbcCollect')} UBC 日志本地路径地址 ${ubcFilePath}`);
            let remoteUbcFile = await this.uploadStaticFile({
                filePath: ubcFilePath,
                bosPath: `/lazycloud/${this.taskId}-${currentTimestamp()}-${uuidv4()}.json`
            });
            this.worker.logger.info(`${getLogTag('closeUbcCollect')} UBC 日志远端地址 ${remoteUbcFile}`);
            this.caseList[0].step[0].result.data.extra.ubdFile = remoteUbcFile;
        }
        catch (err) {
            this.worker.logger.warn(`${getLogTag('closeUbcCollect')} 关闭 UBC 收集失败 ${err.stack}`);
        }
    };

    async clearCommonAlert() {
        try {
            this.worker.logger.info(`${getLogTag('clearCommonAlert')} 通用弹窗点除调用`);
            await this.worker.send('ubc', 'recordUbc', [{ recordTag: this.recordTag, ubcId: 1027 }]);
            await this.worker.send(
                'device',
                'commonAlertClear',
                [{
                    deviceType: this.deviceType,
                    deviceId: this.deviceId,
                    recordTag: this.recordTag,
                    params: {
                        productId: this.productId
                    },
                    checkStatus: false
                }],
                120000
            );
            await this.worker.send('ubc', 'recordUbc', [{ recordTag: this.recordTag, ubcId: 1028 }]);
        }
        catch (err) {
            this.worker.logger.warn(`${getLogTag('clearCommonAlert')} 通用弹窗点除调用失败 ${err.stack}`);
        }
    };

    async clearSysAlert() {
        try {
            this.worker.logger.info(`${getLogTag('clearSysAlert')} 系统弹窗点除调用`);
            await this.worker.send('ubc', 'recordUbc', [{ recordTag: this.recordTag, ubcId: 1023 }]);
            await this.worker.send(
                'device',
                'clearSysAlert',
                [{
                    deviceType: this.deviceType,
                    deviceId: this.deviceId,
                    recordTag: this.recordTag,
                    checkStatus: false
                }],
                120000
            );
            await this.worker.send('ubc', 'recordUbc', [{ recordTag: this.recordTag, ubcId: 1024 }]);
        }
        catch (err) {
            this.worker.logger.warn(`${getLogTag('clearSysAlert')} 系统弹窗点除调用失败 ${err.stack}`);
        }
    };

    async resetDeviceUIStatus() {
        try {
            await this.worker.send(
                'device',
                'resetDeviceUIStatus',
                [{
                    deviceType: this.deviceType,
                    deviceId: this.deviceId,
                    checkStatus: false
                }],
                120000
            );
        }
        catch (err) {
            this.worker.logger.warn(`${getLogTag('resetDeviceUIStatus')} UI 状态重置失败 ${err.stack}`);
        }
        await this.clearSysAlert();
        await this.clearCommonAlert();
    };

    async runStep({ actionInfo }) {
        let { common = {} } = actionInfo;
        let { commonAlertClear = true } = common;
        let result = {};
        try {
            // 检查是否需要通用清除弹窗
            this.sysAlertClear && commonAlertClear ? await this.clearSysAlert() : null;
            this.commonAlertClear && commonAlertClear ? await this.clearCommonAlert() : null;
            // 操作手机完成步骤
            let { screenshot, rect, screenSize, extra = {} } =
                await this.worker.send('device', 'runSingleStep', [{
                    taskInfo: {
                        moduleId: this.moduleId,
                        taskId: this.taskId
                    },
                    deviceType: this.deviceType,
                    deviceId: this.deviceId,
                    step: actionInfo,
                    needDeviceInfo: true,
                    checkStatus: false,
                    paramsList: [...this.paramsList, ...this.hideParamsList],
                    recordTag: this.recordTag
                }], 600000);
            // 构造结果 并 存储结果
            result = {
                status: 0,
                code: 0,
                msg: '',
                data: {
                    screenshot,
                    rect,
                    screenSize,
                    extra
                }
            };
            // 更新参数列表
            if (undefined !== result?.data?.extra?.paramsList?.afterParams) {
                this.paramsList = deepcopy(result.data.extra.paramsList.afterParams);
            }
            if (undefined !== result?.data?.extra?.paramsList) {
                result.data.paramsList = result.data.extra.paramsList;
                delete result.data.extra.paramsList;
            }
        }
        catch (err) {
            // 执行 Error 捕捉
            if (undefined !== err.data) {
                try {
                    // 防止错误 code 异常的情况
                    if (isNaN(err.data.code)) {
                        err.data.code = 700003;
                    }
                    result = {
                        status: -1,
                        code: err.data.code,
                        msg: err.message,
                        err: {
                            type: err.data.type,
                            code: err.data.code,
                            oriMessage: err.data.oriMessage,
                            message: err.data.message,
                            stack: err.data.stack
                        }
                    };
                    // 完成 extra 存储
                    if (err.data.cause) {
                        let extra = err.data.cause;
                        result.data = {};
                        // 截图上传
                        if (undefined !== extra.screenshot) {
                            result.data.screenshot = extra.screenshot;
                            delete extra.screenshot;
                        }
                        if (undefined !== extra.screenSize) {
                            result.data.screenSize = deepcopy(extra.screenSize);
                            delete extra.screenSize;
                        }
                        result.data.extra = extra;
                    }
                }
                catch (error) {
                    result = {
                        status: -1,
                        code: 700004,
                        msg: error.message,
                        err: {
                            message: error.message,
                            stack: error.stack
                        }
                    };
                }
            }
            // 系统 Error 捕捉，可能是乱七八糟的异常错误
            else {
                result = {
                    status: -1,
                    code: 700004,
                    msg: err.message,
                    err: {
                        message: err.message,
                        stack: err.stack
                    }
                };
            }
        }
        return result;
    };

    async runStepHandler({ step, nodeIndex, stepIndex }) {
        let actionInfo = undefined !== step.stepInfo ? jsonParse(step.stepInfo) : jsonParse(step.actionInfo);
        const stepLogTag = `${getLogTag('execute')} (node: ${nodeIndex}, step: ${stepIndex})`;
        const stepStartTime = currentTimestamp();
        this.recordTag = await this.worker.send('ubc', 'openRecord', []);
        let recordTag = this.recordTag;
        // 跑步骤
        this.worker.logger.info(`${stepLogTag} 开始`);
        let result = await this.runStep({ actionInfo });
        this.worker.logger.info(
            `${stepLogTag} 结束 记录: ${this.recordTag}` +
            ` 耗时: ${(currentTimestamp() - stepStartTime) / 1000}s`
        );
        await this.worker.send('ubc', 'endRecord', [{ recordTag: this.recordTag, needUpload: false }]);
        this.recordTag = undefined;
        this.worker.logger.info(`${getLogTag('execute')} (node: ${nodeIndex}, step: ${stepIndex}) 结束`);
        // 处理失败的情况
        if (undefined !== result.err) {
            this.isFinish = true;
            this.resBody.code = result.err.code;
            this.resBody.message = result.err.message;
        }
        return { result, recordTag };
    }

    async uploadStep() {
        const syncStepResult = async ({ step, nodeIndex, stepIndex }) => {
            let result = step.result;
            for (let i = 0; i < 3; i++) {
                try {
                    await new Promise(async (resolve, reject) => {
                        try {
                            zlib.gzip(JSON.stringify({
                                taskId: this.taskId,
                                deviceId: this.phoneId,
                                nodeIndex,
                                stepIndex,
                                code: result.code,
                                result
                            }), (error, zipBuf) => {
                                if (null !== error) {
                                    return reject(error);
                                }
                                cloudServerRequest({
                                    baseURL: global.managerAddress,
                                    path: '/core/cloud/executor/stepReport',
                                    headers: { 'Content-Type': 'application/json', 'Content-Encoding': 'gzip' },
                                    body: zipBuf,
                                    method: 'POST'
                                }).then(res => {
                                    if (0 === res.code) {
                                        return resolve();
                                    }
                                    else {
                                        return reject(new Error(`同步结果错误: ${res.msg}`));
                                    }
                                }).catch(err => reject(err));
                            });
                        }
                        catch (err) {
                            return reject(err);
                        }
                    });
                    break;
                }
                catch (err) {
                    this.worker.logger.warn(
                        `${getLogTag('uplaodStepResult')} try ${i + 1}st 上传步骤结果失败: ${err.stack}`);
                }
            }
        };
        // 上传截图图片并同步结果
        const syncStepScreen = async ({ step, nodeIndex, stepIndex }) => {
            this.worker.logger.info(
                `${getLogTag('syncStepScreen')} (node: ${nodeIndex}, step: ${stepIndex}) 开始上传结果截图`
            );
            let startTime = currentTimestamp();
            if (undefined !== step?.result?.data?.screenshot && '' !== step?.result?.data?.screenshot) {
                step.result.data.screenshot = await this.uploadStaticFile({
                    filePath: step.result.data.screenshot,
                    bosPath: `/lazycloud/${this.taskId}-${currentTimestamp()}-${uuidv4()}.jpg`
                });
            }
            delete step.uploadFlag;
            this.worker.logger.info(
                `${getLogTag('syncStepScreen')} (node: ${nodeIndex}, step: ${stepIndex}) 上传结果截图成功` +
                ` 耗时 ${currentTimestamp() - startTime}ms`
            );
        }
        for (let [nodeIndex, node] of this.caseList.entries()) {
            for (let [stepIndex, step] of node.step.entries()) {
                if (412 === step.stepType && undefined !== step?.result?.data?.extra?.loop) {
                    for (let loopItem of step.result.data.extra.loop) {
                        if (false === loopItem.conditionResult.uploadFlag) {
                            await syncStepScreen({ step: loopItem.conditionResult, nodeIndex, stepIndex });
                        }
                        for (let templateStep of loopItem.stepResult) {
                            if (false !== templateStep.uploadFlag) {
                                continue;
                            }
                            await syncStepScreen({ step: templateStep, nodeIndex, stepIndex });
                        }
                    }
                }
                if (1401 === step.stepType && undefined !== step?.result?.data?.extra?.stepResult) {
                    for (let groupStep of step.result.data.extra.stepResult) {
                        if (false !== groupStep.uploadFlag) {
                            continue;
                        }
                        await syncStepScreen({ step: groupStep, nodeIndex, stepIndex });
                    }
                }
                if (false !== step.uploadFlag) {
                    continue;
                }
                await syncStepScreen({ step, nodeIndex, stepIndex });
                let startTime = currentTimestamp();
                await syncStepResult({ step, nodeIndex, stepIndex });
                this.worker.logger.info(
                    `${getLogTag('uploadStep')} (node: ${nodeIndex}, step: ${stepIndex}) 同步结果成功` +
                    ` 耗时 ${currentTimestamp() - startTime}ms`
                );
            }
        }
        // 上传 Trace 文件
        const syncStepTrace = async ({ step, nodeIndex, stepIndex }) => {
            let startTime = currentTimestamp();
            const uploadStage = async stageItem => {
                if (undefined !== stageItem.screenshot) {
                    stageItem.screenshot = await this.uploadStaticFile({
                        filePath: stageItem.screenshot,
                        bosPath: `/lazycloud/${this.taskId}-${currentTimestamp()}-${uuidv4()}.jpg`
                    });
                }
                if (undefined !== stageItem.pagesource) {
                    let fileName = `${this.taskId}-${currentTimestamp()}-${uuidv4()}`;
                    fs.writeFileSync(`${CACHE_FILE_DIR}/${fileName}.json`, JSON.stringify(stageItem.pagesource));
                    stageItem.pagesource = await this.uploadStaticFile({
                        filePath: `${CACHE_FILE_DIR}/${fileName}.json`,
                        bosPath: `/lazycloud/${fileName}.json`
                    });
                }
                return stageItem;
            };
            /*
             * 暂时封印 Trace 上传代码，暂缓上传 Trace 内容缓解网络问题
             * by wangjiaqi20 2024-10-15
             */
            // let recordTag = step.recordTag;
            // if (undefined === step.result.data) {
            //     step.result.data = {};
            // }
            // let record = await this.worker.send('ubc', 'getRecord', [{ recordTag }]);
            // for (let stage of ['stepInit', 'stepRun', 'stepComplete']) {
            //     for (let [detailIndex, detail] of record[stage].stageInfoList.entries()) {
            //         record[stage].stageInfoList[detailIndex] = await uploadStage(detail);
            //     }
            // }
            // step.result.data.trace = record;
            // step.result.data.trace.recordTag = recordTag;
            delete step.recordTag;
            this.worker.logger.info(
                `${getLogTag('syncStepTrace')} (node: ${nodeIndex}, step: ${stepIndex}) 上传 Trace 成功` +
                ` 耗时 ${currentTimestamp() - startTime}ms`
            );
        };
        for (let [nodeIndex, node] of this.caseList.entries()) {
            for (let [stepIndex, step] of node.step.entries()) {
                if (412 === step.stepType && undefined !== step?.result?.data?.extra?.loop) {
                    for (let loopItem of step.result.data.extra.loop) {
                        if (false === loopItem.conditionResult.uploadFlag) {
                            await syncStepTrace({ step: loopItem.conditionResult, nodeIndex, stepIndex });
                        }
                        for (let templateStep of loopItem.stepResult) {
                            if (false !== templateStep.uploadFlag) {
                                continue;
                            }
                            await syncStepTrace({ step: templateStep, nodeIndex, stepIndex });
                        }
                    }
                }
                if (1401 === step.stepType && undefined !== step?.result?.data?.extra?.stepResult) {
                    for (let groupStep of step.result.data.extra.stepResult) {
                        if (false !== groupStep.uploadFlag) {
                            continue;
                        }
                        await syncStepTrace({ step: groupStep, nodeIndex, stepIndex });
                    }
                }
                if (undefined === step.recordTag) {
                    continue;
                }
                await syncStepTrace({ step, nodeIndex, stepIndex });
            }
        }
    };

    async modelPrice() {
        try {
            let res = await cloudServerRequest({
                baseURL: global.managerAddress,
                path: '/core/ui/model/task/metric',
                body: {
                    taskType: 1,
                    taskId: this.taskId
                },
                headers: global.superHeader,
                method: 'POST'
            });
            if (0 !== res.code) {
                throw new Error(`成本获取失败 ${res.msg}`);
            }
            this.caseList[0].step[0].result.data.extra.priceCost = res.data;
        }
        catch (err) {
            this.worker.logger.warn(`${getLogTag('modelPrice')} 成本记录失败 ${err.stack}`);
        }
    };

    async finishResult() {
        let startTime = currentTimestamp();
        for (let node of this.caseList) {
            for (let step of node.step) {
                if (412 === step.stepType && undefined !== step?.result?.data?.extra?.step) {
                    for (let templateStep of step.result.data.extra.step) {
                        while (undefined !== templateStep.uploadFlag || undefined !== templateStep.recordTag) {
                            await delay(1000);
                        }
                    }
                }
                if (1401 === step.stepType && undefined !== step?.result?.data?.extra?.stepResult) {
                    for (let groupStep of step.result.data.extra.stepResult) {
                        while (undefined !== groupStep.uploadFlag || undefined !== groupStep.recordTag) {
                            await delay(1000);
                        }
                    }
                }
                while (undefined !== step.uploadFlag || undefined !== step.recordTag) {
                    await delay(1000);
                }
            }
        }
        this.worker.logger.info(`${getLogTag('finishResult')} 等待后置结果处理耗时 ${currentTimestamp() - startTime}ms`);
    };

    async readCase(bosLink) {
        let startTime = currentTimestamp();
        let caseInfo = await read(bosLink);
        this.worker.logger.info(`${getLogTag('readCase')} 读取用例文件耗时 ${currentTimestamp() - startTime}ms`);
        if (Array.isArray(caseInfo)) {
            this.caseList = caseInfo;
        }
        else {
            let { ext = {}, caseList, paramsList } = caseInfo;
            let { sysAlertClear = true } = ext;
            this.caseList = caseList;
            this.paramsList = paramsList;
            this.sysAlertClear = sysAlertClear;
        }
    };

    async execute() {
        try {
            // 获取用例信息
            await this.readCase(this.taskInfo.caseFile);
            // 先重置整个设备的 UI 状态
            await this.resetDeviceUIStatus();
            this.worker.logger.info(`${getLogTag('execute')} 设备 UI 状态初始化结束, 任务开始`);
            // 检查是否需要开启 ADBLOG 录制
            await this.openLogCat();
            // 检查是否需要录屏开启
            await this.openScreenRecord();
            // 检查是否需要开启 UBC 录制
            await this.openUbcCollect();
            // 执行节点
            for (let [nodeIndex, node] of this.caseList.entries()) {
                this.worker.logger.info(`${getLogTag('execute')} (node: ${nodeIndex}) 开始`);
                // 执行步骤
                for (let [stepIndex, step] of node.step.entries()) {
                    if (this.isFinish) {
                        break;
                    }
                    let { result: stepResult, recordTag } =
                        await this.runStepHandler({ step, nodeIndex, stepIndex });
                    step.result = stepResult;
                    step.recordTag = recordTag;
                    step.uploadFlag = false;
                }
                this.worker.logger.info(`${getLogTag('execute')} (node: ${nodeIndex}) 结束`);
                if (this.isFinish) {
                    break;
                }
            }
            // 初始化最终信息格式位置
            if (undefined === this.caseList[0].step[0].result?.data?.extra) {
                if (undefined === this.caseList[0].step[0].result?.data) {
                    this.caseList[0].step[0].result.data = {};
                }
                this.caseList[0].step[0].result.data.extra = {};
            }
            // 关闭 ADBLOG 录制，并上传日志
            await this.closeLogCat({ needReturn: true });
            // 关闭录屏并上传文件
            await this.closeScreenRecord({ needReturn: true });
            // 上传 UBC 结果
            await this.closeUbcCollect();
            // 获取大模型成本信息
            await this.modelPrice();
            // 完成最后的结果采集
            await this.finishResult();
            // 上传结果
            let fileName = `${this.taskId}-result-${currentTimestamp()}-${uuidv4()}.json`;
            let filePath = `${CACHE_FILE_DIR}/${fileName}`;
            fs.writeFileSync(filePath, JSON.stringify(this.caseList));
            this.resBody.result.resBos = await this.uploadStaticFile({ filePath, bosPath: `/lazycloud/${fileName}` });
        }
        catch (err) {
            this.resBody.code = 700002;
            this.resBody.message = err.message;
            this.worker.logger.error(`${getLogTag('execute')} 任务执行异常 ${err.stack}`);
        }
        finally {
            if (undefined === this.resBody.code) {
                this.resBody.code = 700003;
            }
            try {
                await this.closeLogCat({ needReturn: false });

            }
            catch { }
            try {
                await this.closeScreenRecord({ needReturn: false });
            }
            catch { }
            try {
                await this.closeUbcCollect();
            }
            catch { }
        }
        return this.resBody;
    };
}

module.exports = { QAMateTask };