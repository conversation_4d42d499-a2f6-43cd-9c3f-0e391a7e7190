/**
 * @create 王家麒@2022.06.01
 */
const fs = require('fs');
const zlib = require('zlib');
const { v4: uuidv4 } = require('uuid');
const { time: { currentTimestamp, delay }, json: { jsonParse } } = require('@baidu/bat-util');

const { deepcopy } = require('../../../util/copy');
const { read, uploadFileAndDelete } = require('../../../util/file');
const { cloudServerRequest } = require('../../../util/request');
const { CACHE: { CACHE_FILE_DIR } } = require('../../../config');

const getLogTag = method => `[worker.task.run.monkey.${method}]`;

class MonkeyTask {
    constructor({ taskId, taskInfo, deviceType, deviceId, phoneId, worker }) {
        this.moduleId = taskInfo.moduleId;
        this.planId = taskInfo.planId;
        this.taskId = taskId;
        this.taskInfo = taskInfo;
        this.deviceType = deviceType;
        this.deviceId = deviceId;
        this.phoneId = phoneId;
        this.worker = worker;
        this.osType = 1;
        if (this.deviceType === 'iOS') {
            this.osType = 2;
        }
        this.deviceHandler = this.worker.getDevice(deviceType, deviceId);

        this.executeTime = taskInfo.extra.taskExecuteTime;
        this.agentTaskId = undefined;
        this.agentStartTime = undefined;
        this.isFinish = false;
        this.isCrashStart = false;
        this.crashList = [];

        this.resBody = {
            code: 0,
            message: 'success',
            result: {}
        };
    }

    async crashListenerMonitor() {
        return new Promise(async (resolve, reject) => {
            while (false === this.isFinish) {
                try {
                    // 安卓需要实时检测是否开启并拼接之前的崩溃信息
                    if (1 === this.osType) {
                        this.isCrashStart = (await deviceHandler.crashStatus()).isStart;
                        if (false === this.isCrashStart && false === this.isFinish) {
                            this.worker.logger.info(`${getLogTag('crashListenerMonitor')} 崩溃检测到中断`);
                            let lastTimesCrashList = (await deviceHandler.crashInfo()).crashList;
                            this.crashList = [...this.crashList, ...lastTimesCrashList];
                        }
                    }
                    // 如果没开启崩溃检测就打开
                    if (false === this.isCrashStart && false === this.isFinish) {
                        this.worker.logger.info(`${getLogTag('crashListenerMonitor')} 崩溃检测开启`);
                        await this.deviceHandler.crash();
                        this.isCrashStart = true;
                    }
                }
                catch (err) {
                    this.worker.logger.warn(`${getLogTag('crashListenerMonitor')} 崩溃启用检测失败 ${err.stack}`);
                }
                finally {
                    // 冷静 5s 再次检测
                    await delay(5000);
                }
            }
        });
    };

    async stopCrashListener() {
        if (1 === this.osType) {
            this.isCrashStart = (await deviceHandler.crashStatus()).isStart;
            if (this.isCrashStart) {
                await deviceHandler.closeCrash();
                let lastTimesCrashList = (await deviceHandler.crashInfo()).crashList;
                this.crashList = [...this.crashList, ...lastTimesCrashList];
            }
        }
        else if (2 === this.osType) {
            // 用云控里的获取信息的封装函数，直接可以获取到解析后的信息
            this.crashList = await this.worker.send(
                `dorking_${this.phoneId}`,
                'getCrashInfo',
                [{ mappingFile: this.taskInfo.extra.crashParams.mappingPath ?? '' }],
                600000
            );
        }
    }

    async startAgent() {
        let res = await cloudServerRequest({
            baseURL: global.monkeyAgentAddress,
            path: '/qmonkey/task/start',
            body: {
                module_id: this.moduleId,
                plan_id: this.planId,
                task_id: this.taskId,
                entrance_type: 1,
                entrance_params: {
                    case_file: this.taskInfo.caseFile
                },
                package_name: taskInfo.extra.crashParams.packageName,
                execute_time: this.executeTime,
                device_type: this.osType,
                device_id: this.phoneId,
                extra: {
                    setupParams: this.taskInfo.extra.setupParams,
                    executeParams: this.taskInfo.extra.executeParams
                }
            },
            method: 'POST'
        });
        if (0 !== res.errno) {
            throw new Error(`创建Agent任务失败 ${res.errmsg}`);
        }
        this.agentStartTime = currentTimestamp();
        this.agentTaskId = res.data.id;
    };

    async resetDeviceUIStatus() {
        try {
            await this.worker.send(
                'device',
                'resetDeviceUIStatus',
                [{
                    deviceType: this.deviceType,
                    deviceId: this.deviceId,
                    checkStatus: false
                }],
                120000
            );
        }
        catch (err) {
            this.worker.logger.warn(`${getLogTag('resetDeviceUIStatus')} UI 状态重置失败 ${err.stack}`);
        }
        await this.clearSysAlert();
        await this.clearCommonAlert();
    };

    async readCase(bosLink) {
        let startTime = currentTimestamp();
        let caseInfo = await read(bosLink);
        this.worker.logger.info(`${getLogTag('readCase')} 读取用例文件耗时 ${currentTimestamp() - startTime}ms`);
        let { caseList, paramsList } = caseInfo;
        this.caseList = caseList;
        this.paramsList = paramsList;
    };

    async execute() {
        try {
            // 获取用例信息
            await this.readCase(this.taskInfo.caseFile);
            // 先重置整个设备的 UI 状态
            await this.resetDeviceUIStatus();
            this.worker.logger.info(`${getLogTag('execute')} 设备 UI 状态初始化结束, 任务开始`);
            // 开启崩溃监听
            crashListenerMonitor();
            // 开启 Agent
            await startAgent();
            // 监听结果
            await new Promise(async (resolve, reject) => {
                // 在耗时内主动轮询
                while (currentTimestamp() - this.agentStartTime >= (this.executeTime + 2) * 60 * 1000) {
                    try {
                        let res = await cloudServerRequest({
                            baseURL: global.monkeyAgentAddress,
                            path: '/qmonkey/task/status/fetch',
                            body: {
                                id: this.agentTaskId
                            },
                            method: 'POST'
                        });
                        if (0 !== res.errno) {
                            throw new Error(`获取失败 ${res.errmsg}`);
                        }
                        if (2 === res.data.status) {
                            return resolve();
                        }
                    }
                    catch (err) {
                        this.worker.logger.warn(`${getLogTag('execute')} 获取任务结果异常 ${err.stack}`);
                    }
                    finally {
                        // 冷静 30s 再次获取
                        await delay(30000);
                    }
                }
                // 理论上不应该到达这里，如果到达了说明 Agent 没有按照约定耗时切断整体任务，主动发送关闭信号
                await cloudServerRequest({
                    baseURL: global.monkeyAgentAddress,
                    path: '/qmonkey/task/cancel',
                    body: {
                        id: this.agentTaskId
                    },
                    method: 'POST'
                });
            }).catch(err => { throw err; });
            this.isFinish = true;
            // 冷静两秒等待所有监听器完成数据获取
            await delay(2000);
            this.stopCrashListener();
            // 上传结果
            let fileName = `${this.taskId}-result-${currentTimestamp()}-${uuidv4()}.json`;
            let filePath = `${CACHE_FILE_DIR}/${fileName}`;
            fs.writeFileSync(filePath, JSON.stringify(this.caseList));
            this.resBody.result.resBos = await this.uploadStaticFile({ filePath, bosPath: `/lazycloud/${fileName}` });
        }
        catch (err) {
            this.resBody.code = 700002;
            this.resBody.message = err.message;
            this.worker.logger.error(`${getLogTag('execute')} 任务执行异常 ${err.stack}`);
            this.isFinish = true;
        }
        finally {
            if (undefined === this.resBody.code) {
                this.resBody.code = 700003;
            }
        }
        return this.resBody;
    };
}

module.exports = { MonkeyTask };