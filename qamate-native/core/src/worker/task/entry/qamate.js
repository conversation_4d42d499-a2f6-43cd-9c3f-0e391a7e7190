/**
 * @create 王家麒@2023.02.15
 */
const { setTimeout, clearTimeout } = require('timers');
const { time: { currentTimestamp, delay } } = require('@baidu/bat-util');
const { QAMateTask } = require('../run/qamate');
const { cloudServerRequest } = require('../../../util/request');

const MAX_TASK_TIMEOUT_MINUTE = 25;

const getLogTag = method => `[worker.task.entry.qamate.${method}]`;

const runTask = async ({
    taskId,
    taskInfo,
    deviceType,
    deviceId,
    phoneId,
    worker
}) => {
    let timeout = undefined;
    taskInfo = JSON.parse(taskInfo);
    let resBody = {
        deviceId: phoneId,
        taskId: parseInt(taskId, 10),
        type: taskInfo.type,
        code: 0,
        message: 'success',
        result: {}
    };
    let startTime = currentTimestamp();
    try {
        worker.logger.info(`${getLogTag('runTask')} id ${taskId} 开始执行`);
        let isEnd = false;
        const task = new QAMateTask({
            taskId,
            taskInfo,
            deviceType,
            deviceId,
            phoneId,
            worker
        });
        task.execute().then(({ code, message, result }) => {
            resBody.code = code;
            resBody.message = message;
            resBody.result = result;
            isEnd = true;
        }).catch((err) => {
            isEnd = true;
            throw err;
        });
        let { taskTimeout = MAX_TASK_TIMEOUT_MINUTE } = taskInfo;
        await new Promise(async (resolve, reject) => {
            timeout = setTimeout(
                () => reject(new Error('qamate 任务执行超时')),
                taskTimeout * 60 * 1000
            );
            while (!isEnd) {
                try {
                    await task.uploadStep();
                }
                catch (err) {
                    worker.logger.warn(`${getLogTag('runTask')} 步骤上传异常 ${err.stack}`);
                }
                finally {
                    await delay(500);
                }
            }
            if (isEnd) {
                resolve();
            }
        }).catch(err => { throw err; });
    }
    catch (err) {
        resBody.code = 700001;
        resBody.message = err.message;
    }
    finally {
        undefined !== timeout ? clearTimeout(timeout) : null;
        // 日志结果
        worker.logger.info(
            `${getLogTag('runTask')} id ${taskId} 执行完毕 ` +
            `结果 ${JSON.stringify(resBody)} ` +
            `耗时 ${(currentTimestamp() - startTime) / 1000}s`
        );
        let isCallback = false;
        // 回传结果 重试 3 次
        for (let index = 0; index < 3; index++) {
            try {
                let res = await cloudServerRequest({
                    baseURL: global.managerAddress,
                    path: '/core/cloud/executor/callback',
                    body: resBody,
                    method: 'POST'
                });
                if (0 !== res.code) {
                    throw new Error(`结果回调失败 ${res.msg}`);
                }
                isCallback = true;
                break;
            }
            catch (err) {
                worker.logger.info(`${getLogTag('runTask')} id ${taskId} 结果回调失败 trys ${index + 1} ${err.stack}`);
                await delay(1000);
            }
        }
        worker.logger.info(`${getLogTag('runTask')} id ${taskId} 最终回调状态 ${isCallback}`);
    }
};

module.exports = { runTask };