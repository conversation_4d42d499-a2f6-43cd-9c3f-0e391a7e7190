/**
 * @create 王家麒@2023.02.15
 */
const LazyBaseWorker = require('../../lib/lazy-worker/LazyBaseWorker');
const { runTask: runLazyOneTask } = require('./entry/lazyone');
const { runTask: runLazyOneDebugTask } = require('./entry/lazyone-debug');
const { runTask: runQAMateTask } = require('./entry/qamate');
const { runTask: runGenerateTask } = require('./entry/generate');
const { runTask: runMonkeyTask } = require('./entry/monkey');
const { runTask: runKnowledgeTak } = require('./entry/knowledge');
const { logger } = require('./logger');

class TaskWorker extends LazyBaseWorker {
    constructor({ name, fatherName }, { deviceType, deviceId, phoneId, taskId, taskType, taskInfo }) {
        super({ name, fatherName, logger });
        this.deviceType = deviceType;
        this.deviceId = deviceId;
        this.phoneId = phoneId;
        this.taskId = taskId;
        this.taskType = taskType;
        this.taskInfo = taskInfo;

        // 注册路由
        this.controller = Object.assign({
            'run': 'run'
        }, this.controller);
    }

    async run() {
        if (1 === this.taskType) {
            await runLazyOneTask({
                taskId: this.taskId,
                taskInfo: this.taskInfo,
                deviceType: this.deviceType,
                deviceId: this.deviceId,
                worker: this
            });
        }
        else if (2 === this.taskType) {
            await runLazyOneDebugTask({
                taskId: this.taskId,
                taskInfo: this.taskInfo,
                deviceType: this.deviceType,
                deviceId: this.deviceId,
                worker: this
            });
        }
        else if (3 === this.taskType) {
            await runQAMateTask({
                taskId: this.taskId,
                taskInfo: this.taskInfo,
                deviceType: this.deviceType,
                deviceId: this.deviceId,
                phoneId: this.phoneId,
                worker: this
            });
        }
        else if (5 === this.taskType) {
            await runGenerateTask({
                taskId: this.taskId,
                taskInfo: this.taskInfo,
                deviceType: this.deviceType,
                deviceId: this.deviceId,
                phoneId: this.phoneId,
                worker: this
            });
        }
        else if (6 === this.taskType) {
            await runMonkeyTask({
                taskId: this.taskId,
                taskInfo: this.taskInfo,
                deviceType: this.deviceType,
                deviceId: this.deviceId,
                worker: this
            });
        }
        else if (7 === this.taskType) {
            await runKnowledgeTak({
                taskId: this.taskId,
                taskInfo: this.taskInfo,
                worker: this
            });
        }
        else {
            throw new Error(`不支持该任务类型 ${this.taskType}`);
        }
    };
}

const init = async ({ name, fatherName }, { deviceType, deviceId, phoneId, taskId, taskType, taskInfo }) => {
    worker = new TaskWorker({ name, fatherName }, { deviceType, deviceId, phoneId, taskId, taskType, taskInfo });
    await worker.ready();
}

const get = () => worker;

module.exports = { init, get };