/**
 * @create 王家麒@2022.06.09
 */
const { time: { delay } } = require('@baidu/bat-util');

const systemAction = require('./step/system');
const coordinateAction = require('./step/coordinate');
const manualAction = require('./step/manual');
const patternWidgetAction = require('./step/pattern');
const niubillityPatternWidgetAction = require('./step/niubillity-pattern');
const niubillityV2PatternWidgetAction = require('./step/niubillity-pattern-v2');
const mercuryPatternWidgetAction = require('./step/mercury-pattern');
const andromedaPatternWidgetAction = require('./step/andromeda-pattern');
const venusPatternWidgetAction = require('./step/venus-pattern');
const marsPatternWidgetAction = require('./step/mars-pattern');
const jupiterPatternWidgetAction = require('./step/jupiter-pattern');

const { mergeParams, createStepConstParams } = require('./util/index');
const { handleStepPramasByParams } = require('../replay/util/params');
const { cacheDeviceScreenshot, decodeWebBase64 } = require('../../../util/image');
const { cloudServerRequest } = require('../../../util/request');
const { deepcopy } = require('../../../util/copy');
const { read } = require('../../../util/file');
const { OneError } = require('../../../util/error');

const getLogTag = method => `[device.run-step.${method}]`;

const runStep = async ({
    device,
    dom,
    recordTag,
    stepInfo,
    worker,
    needDeviceInfo,
    useDomCache,
    fishLogInfo,
    paramsList,
    needCompleteAction,
    taskInfo
}) => {
    const logTag = `${getLogTag('runStep')} 设备 ${device.id}`;
    let error = false;
    try {
        // 步骤初始化处理
        stepInfo = await setupStepInfo({ stepInfo });
        worker.logger.info(`${logTag} 完成步骤初始化预处理`);
        // 根据参数计算现有步骤参数
        stepInfo = await handleStepPramasByParams({
            actionInfo: stepInfo,
            paramsList,
            worker
        });
        worker.logger.info(`${logTag} 完成步骤参数替换预处理`);
        stepInfo.params.recordTag = recordTag;
        stepInfo.params.taskInfo = taskInfo;
        // 具体执行
        dom.setDomCache(useDomCache);
        worker.cancelDeviceStatus(device.type, device.id, false);
        // 系统操作
        if (1 === stepInfo.type) {
            return await systemAction({ device, stepParams: stepInfo.params, paramsList, worker, needDeviceInfo });
        }
        // 控件操作
        else if (2 === stepInfo.type) {
            return await patternWidgetAction(device, dom, stepInfo.params, needDeviceInfo, fishLogInfo);
        }
        // 人工操作
        else if (3 === stepInfo.type) {
            return await manualAction(device, stepInfo.params, worker);
        }
        // 坐标操作
        else if (4 === stepInfo.type) {
            return await coordinateAction(device, stepInfo.params, needDeviceInfo);
        }
        // 多控件协同操作（NB DOM）
        else if (5 === stepInfo.type) {
            return await niubillityPatternWidgetAction(
                device, dom, stepInfo.params, needDeviceInfo, fishLogInfo);
        }
        // 多控件协同操作 V2（NB DOM） -> BatDom 9.0
        else if (6 === stepInfo.type) {
            return await niubillityV2PatternWidgetAction(
                device, dom, stepInfo.params, needDeviceInfo, fishLogInfo);
        }
        // Mercury Dom -> BatDom 9.1
        else if (7 === stepInfo.type) {
            return await mercuryPatternWidgetAction(
                device, dom, stepInfo.params, needDeviceInfo, fishLogInfo);
        }
        // Andromeda Dom -> BatDom 9.4.1 (网格建模)
        else if (8 === stepInfo.type) {
            return await andromedaPatternWidgetAction(
                device, dom, stepInfo.params, needDeviceInfo, fishLogInfo);
        }
        // Venus Dom -> BatDom 10.0.0（通用建模）
        else if (9 === stepInfo.type) {
            return await venusPatternWidgetAction(
                device, dom, stepInfo.params, needDeviceInfo, fishLogInfo);
        }
        // Mars Dom -> BatDom 11.0.0（通用建模）
        else if (10 === stepInfo.type) {
            return await marsPatternWidgetAction(
                device, dom, stepInfo.params, needDeviceInfo);
        }
        // Jupiter Dom -> AIDom 1.0.0（智能定位）
        else if (11 === stepInfo.type) {
            return await jupiterPatternWidgetAction(
                device, dom, stepInfo.params, needDeviceInfo);
        }
        else {
            throw new Error(`${logTag} 不支持步骤类型 ${stepInfo.type}`);
        }
    }
    catch (err) {
        error = err;
        worker.logger.error(`${logTag} 执行步骤失败 ${error.stack}`);
        if (error.cause && error.cause.screenshot) {
            error.cause.screenshot = cacheDeviceScreenshot(device.id, await decodeWebBase64(error.cause.screenshot));
        }
        // 兜底一个主动取消错误码
        if ('主动取消' === error.message || '主动取消' === error.oriMessage) {
            error = new OneError(error, 999100, '用户主动取消行为');
        }
    }
    finally {
        worker.logger.info(`${logTag} 执行步骤结束`);
        dom.setDomCache(false);
        worker.cancelDeviceStatus(device.type, device.id, false);
        if (error) {
            throw error;
        }
        if (needCompleteAction) {
            try {
                let { common = {} } = stepInfo;
                let { stepInterval = 2 } = common;
                stepInterval = stepInterval >= 500 ? stepInterval / 1000 : stepInterval;
                await delay(stepInterval * 1000);
                let screenSize = await device.size();
                screenSize.rotation = await device.screenRotation();
                let screenshot = await device.screenshot();
                await worker.ubcTrace({
                    recordTag,
                    traceKey: 'stepComplete',
                    traceItem: {
                        key: 'stepInterval',
                        name: `步骤间等待 ${stepInterval} 秒`,
                        code: 0,
                        msg: '执行成功',
                        screenshot,
                        screenSize
                    }
                });
            }
            catch (err) {
                worker.logger.warn(`${logTag} 执行后置等待动作失败 ${err.stack}`);
            }
        }
    }
};

const setupStepInfo = async ({ stepInfo }) => {
    const readLinkContent = async link => {
        for (let i = 0; i < 3; i++) {
            try {
                return await read(link);
            }
            catch { }
        }
        throw new Error('读取步骤基本资源信息失败');
    };
    if ('string' === typeof stepInfo) {
        stepInfo = JSON.parse(stepInfo);
    }
    // BatDom 11 之前的格式
    if (
        undefined !== stepInfo?.params?.dom
        && 'string' === typeof stepInfo.params.dom
        && stepInfo.params.dom.startsWith('http')
    ) {
        stepInfo.params.dom = await readLinkContent(stepInfo.params.dom);
        if ('string' === typeof stepInfo.params.dom) {
            stepInfo.params.dom = JSON.parse(stepInfo.params.dom);
        }
    }
    // BatDom 11 及之后的格式
    if (
        undefined !== stepInfo?.params?.recordInfo?.dom
        && 'string' === typeof stepInfo.params.recordInfo.dom
        && stepInfo.params.recordInfo.dom.startsWith('http')
    ) {
        stepInfo.params.recordInfo.dom = await readLinkContent(stepInfo.params.recordInfo.dom);
        if ('string' === typeof stepInfo.params.dom) {
            stepInfo.params.recordInfo.dom = JSON.parse(stepInfo.params.recordInfo.dom);
        }
    }
    return stepInfo;
};

module.exports = async ({
    device,
    dom,
    stepInfo,
    worker,
    needDeviceInfo = true,
    useDomCache = false,
    fishLogInfo = {},
    paramsList = [],
    recordTag = undefined,
    needCompleteAction = true,
    taskInfo = {}
}) => {
    const logTag = `${getLogTag('index')} 设备 ${device.id}`;
    worker.logger.info(`${logTag} 准备执行步骤`);
    await worker.ubcLog({ recordTag, ubcId: 1000 });
    // 特殊参数
    let specialParamsList = [];
    // 判断是否有剪切板操作，有的话获取剪切板内容
    let clipboardContent = '';
    try {
        if (JSON.stringify(stepInfo).includes('${ONE_CLIPBOARD}')) {
            clipboardContent = await device.clipboardContent();
        }
    }
    catch { }
    finally {
        specialParamsList.push({
            name: 'ONE_CLIPBOARD',
            value: clipboardContent
        });
    }
    // 判断是否有获取 CUID 的操作
    try {
        let match = JSON.stringify(stepInfo).match(/\$\{ONE_(.*?)_CUID\}/);
        if (match) {
            let appName = match[1];
            let cuid = '';
            let deviceId = worker.getPhoneId(device.type, device.id);
            try {
                let res = await cloudServerRequest({
                    baseURL: global.deviceAddress,
                    path: '/core/device/deviceExt/getDeviceCuid',
                    body: { deviceId, token: 'E5FEA98991E1EACF7EE801292C6150F8' },
                    headers: global.superHeader,
                    method: 'POST'
                });
                if (0 !== res.code) {
                    throw new Error(`获取 ${deviceId} 设备 CUID 信息失败 ${res.msg}`);
                }
                for (let key in res.data.cuidMap) {
                    if (key.toLowerCase() === appName.toLowerCase()) {
                        cuid = res.data.cuidMap[key];
                        break;
                    }
                }
            }
            catch { }
            finally {
                specialParamsList.push({
                    name: `ONE_${appName}_CUID`,
                    value: cuid
                });
            }
        }
    }
    catch { }
    // 参数初始化处理
    let beforeParams = await mergeParams({
        oriParams: deepcopy(paramsList),
        setParams: [
            ...await createStepConstParams({ device, worker }),
            ...specialParamsList
        ]
    });
    worker.logger.info(`${logTag} 预处理完毕, 开始执行`);
    // 获取结果
    let res = await runStep({
        device,
        dom,
        recordTag,
        stepInfo,
        worker,
        needDeviceInfo,
        useDomCache,
        fishLogInfo,
        paramsList: beforeParams,
        needCompleteAction,
        taskInfo
    });
    worker.logger.info(`${logTag} 执行完毕, 开始后置处理`);
    if (undefined === res.extra) {
        res.extra = {};
    }
    // 老版本 extra 里有部分内容是数组形式，做个转变，但理论上这些数据应该无关紧要
    if (Array.isArray(res.extra)) {
        res.extra = {
            extraInfo: res.extra
        };
    }
    // 处理执行后的参数表
    let afterParams = undefined !== res.extra.setParams ?
        await mergeParams({ oriParams: beforeParams, setParams: res.extra.setParams }) : deepcopy(beforeParams);
    res.extra.paramsList = {
        beforeParams: beforeParams.filter(item => true !== item.hide),
        afterParams: afterParams.filter(item => true !== item.hide)
    };
    worker.logger.info(`${logTag} 执行完毕, 开始后置处理完成`);
    await worker.ubcLog({ recordTag, ubcId: 1002 });
    return res;
};