/**
 * @create 王家麒@2022.12.14
 */
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { time: { currentTimestamp }, file: { mkdir, rmdir } } = require('@baidu/bat-util');
const { mergeVideo } = require('../../../../util/image');
const { checkOneDep } = require('../../../../lib/env-check');
const { CACHE: { CACHE_DEVICE_SCREENSHOT_DIR } } = require('../../../../config');

module.exports = async ({ device, params }) => {
    if (!(await checkOneDep('ffmpeg') && await checkOneDep('scrcpy'))) {
        throw new Error('ffmpeg, scrcpy 依赖未安装, 请先 brew install scrcpy 安装');
    }
    if (true === params.needClose) {
        let recordRes = await device.handler.closeRecordFrames('android' === device.type.toLowerCase() ? false : true);
        // 安卓 设备可以直接拿到视频 MP4 格式的视频文件, 直接返回即可
        // iOS 设备需要将录制后的帧图片合成视频
        let recordFile = 'android' === device.type.toLowerCase() ? recordRes.videoPath : await mergeVideo({
            imageList: recordRes.frames,
            storeDir: recordRes.storeDir,
            needClear: true
        });
        // 将视频复制出来，源目录直接删除，节省内存
        let newRecordFilePath = `${CACHE_DEVICE_SCREENSHOT_DIR}/${device.id}_${currentTimestamp()}_${uuidv4()}.mp4`;
        fs.copyFileSync(recordFile, newRecordFilePath);
        rmdir(recordRes.storeDir);
        return { recordFile: newRecordFilePath };
    }
    let recordDir = `${CACHE_DEVICE_SCREENSHOT_DIR}/${device.id}_${currentTimestamp()}_${uuidv4()}`;
    mkdir(recordDir);
    return await device.handler.recordFrames(recordDir, { highFps: false });
};
