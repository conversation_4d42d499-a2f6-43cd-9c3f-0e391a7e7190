/**
 * @create 王家麒@2020.06.06
 */
const fs = require('fs');
const { time: { delay } } = require('@baidu/bat-util');
const { get: getWorker } = require('../worker');
const { get: getNetwork } = require('../../../util/network');
const { cloudServerRequest } = require('../../../util/request');
const { PROFILE: { SYS_PROFILE_DIR } } = require('../../../config');
const { logger } = require('../logger');

const registerLazyOne = () =>
    new Promise(async (resolve, reject) => {
        const worker = getWorker();
        while (true) {
            try {
                if (null !== worker.getWorkMode()) {
                    break;
                }
                await worker.registerLazyOne();
                let workMode = 1;
                let dorkingMode = 1;
                if (fs.existsSync(`${SYS_PROFILE_DIR}/mode.json`)) {
                    try {
                        ({ workMode = 1, dorkingMode = 1 } = JSON.parse(fs.readFileSync(`${SYS_PROFILE_DIR}/mode.json`)));
                    }
                    catch (err) {
                        logger.warn(`缓存 mode 信息获取失败, 默认走工具模式 ${err.stack}`);
                    }
                }
                else {
                    logger.warn('无缓存 mode 信息, 默认走工具模式');
                }
                if (3 === workMode) {
                    await worker.initWorkMode(2, 2, 2);
                }
                else {
                    await worker.initWorkMode(workMode, 2 === workMode ? 2 : 1, dorkingMode);
                }
                break;
            }
            catch (err) {
                logger.error(`LazyOne 设备注册失败 ${err.stack}`);
            }
            await delay(1000);
        }
        return resolve(true);
    });

const heartbeat = async lazyOneId => {
    const worker = getWorker();
    const getOnlineDevice = async type => {
        let onlineDeviceId = [];
        let devicesMap = worker.getDeviceMap(type);
        for (let id in devicesMap) {
            let device = devicesMap[id];
            // 0 未初始化；1 初始化中；2 空闲；3 繁忙；4 异常；5 离线；6 离线中
            if ([2, 3].includes(device.status)) {
                onlineDeviceId.push(device.phoneId);
            }
        }
        return onlineDeviceId;
    };

    // 获取 IP 信息
    let { networkList } = getNetwork();
    if (0 === networkList.length) {
        throw new Error('获取网络信息失败');
    }
    // 上报心跳
    let res = await cloudServerRequest({
        baseURL: global.deviceAddress,
        path: '/core/device/device/heartbeat',
        body: {
            deviceId: lazyOneId,
            ip: networkList[0].ip,
            onlineDeviceList: [...await getOnlineDevice('ios'), ...await getOnlineDevice('android')],
        },
        headers: global.superHeader,
        method: 'POST'
    });
    if (0 !== res.code) {
        throw new Error(`上报工具心跳信息失败 ${res.msg}`);
    }
};

module.exports = { registerLazyOne, heartbeat };