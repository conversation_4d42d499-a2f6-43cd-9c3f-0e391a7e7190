/**
 * @create 王家麒@2020.08.08
 */
const fs = require('fs');
const path = require('path');
const { setTimeout, clearTimeout } = require('timers');
const { v4: uuidv4 } = require('uuid');
const {
    time: { currentTimestamp, delay },
    file: { mkdir },
    shell: { exeSync },
    error: { ConfigError }
} = require('@baidu/bat-util');

const LazyBaseWorker = require('../../lib/lazy-worker/LazyBaseWorker');
const Dom = require('./dom/dom-handler');
const stepRun = require('./run-step/index');
const clearDeviceAlert = require('./actions/clear-system-alert');
const clearCommonAlert = require('./actions/clear-common-alert');
const { runDebugTask } = require('./replay/debug/index');
const { getDeviceType } = require('./util/device');
const { get: getNetwork } = require('../../util/network');
const { cloudServerRequest } = require('../../util/request');
const { deepcopy } = require('../../util/copy');
const { zip, uploadFileAndDelete } = require('../../util/file');
const { logger } = require('./logger');
const {
    ENV,
    IS_DEBUG,
    IS_LOCALTOOL,
    APPLE,
    PROFILE: { SYS_PROFILE_DIR, PROXY_PROFILE_DIR, DEVICE_PROFILE_DIR },
    CACHE: { CACHE_DEVICE_SCREENSHOT_DIR, CACHE_FILE_DIR }
} = require('../../config');

const automator = IS_DEBUG ? require('../../../../ui-automator/src/index') : require('@baidu/bat-automator');

const STATUS_STAGE = {
    orderList: ['register', 'init', 'proxy', 'lastCheck'],
    register: {
        // 0 未开始 1 进行中 2 正常 3 异常
        status: 0,
        msg: ''
    },
    init: {
        // 0 未开始 1 进行中 2 正常 3 异常
        status: 0,
        msg: ''
    },
    proxy: {
        // 0 未开始 1 进行中 2 初始化成功, 待配置 3 配置成功.... 4 异常
        status: 0,
        tryTimes: 0,
        lastTryTime: 0,
        msg: '',
        staticUrl: '',
        pacUrl: ''
    },
    lastCheck: {
        // 0 未开始 1 进行中 2 成功 3 异常
        status: 0,
        msg: ''
    }
};

let worker = null;

const getLogTag = method => `[worker.${method}]`;

class DeviceWorker extends LazyBaseWorker {
    constructor({ name, fatherName }, { resourcePath }) {
        super({ name, fatherName, logger });
        this.deviceMap = { android: {}, iOS: {} };
        this.mode = null; // 工作模式 1: 工具 2: 机房
        this.cloudServerMode = null; // 云端策略任务消费 1: 关闭 2: 开启
        this.dorkingMode = null; // 云控模式 1: 关闭 2: 开启
        this.lazyOneId = null;
        this.appleOption = {};
        this.gotCertificate = false;
        this.workingCloudTask = {};
        this.workingDebugTask = {};
        this.cancelTaskList = [];

        // 注册路由
        this.controller = Object.assign({
            'updateWorkMode': 'updateWorkMode',
            'getWorkMode': 'getWorkMode',
            'updateDorkingMode': 'updateDorkingMode',
            'getDorkingMode': 'getDorkingMode',
            'initWorkMode': 'initWorkMode',

            'getLazyOneId': 'getLazyOneId',
            'getPhoneId': 'getPhoneId',

            'runSingleStep': 'runSingleStep',

            'clearSysAlert': 'clearSysAlert',
            'commonAlertClear': 'commonAlertClear',
            'resetDeviceUIStatus': 'resetDeviceUIStatus',

            'autoCheckProxy': 'autoCheckProxy',
            'offlineDevice': 'offlineDevice',

            'generateDebugCase': 'generateDebugCase',
            'cancelDebugTask': 'cancelDebugTask',

            'startRecordAction': 'startRecordAction',
            'stopRecordAction': 'stopRecordAction',

            'domCall': 'domCall',
            'deviceCall': 'deviceCall',
        }, this.controller);

        this.updateXcodeEnvironment(resourcePath);
    }

    /*
     * --------------------
     * 重写父类方法相关
     * --------------------
     */
    async closeWorker(code) {
        try {
            await this.offlineAllDevice();
        }
        catch { }
        finally {
            await super.closeWorker(code);
        }
    }


    /*
     * --------------------
     * 运行环境准备相关
     * --------------------
     */
    updateXcodeEnvironment(resourcesPath) {
        this.appleOption = APPLE;
        this.appleOption.wdaProjectPath = '';
        const rootPath = IS_DEBUG ? `${__dirname}/../../..` : resourcesPath;
        const updateWdaProjectPath = resourcesPath => {
            this.appleOption.wdaProjectPath =
                IS_DEBUG ?
                    path.join(__dirname, '../../../../wda', 'WebDriverAgent.xcodeproj') :
                    path.join(resourcesPath, 'public/bat-wda', 'WebDriverAgent.xcodeproj');
            if (!fs.existsSync(this.appleOption.wdaProjectPath)) {
                this.appleOption.wdaProjectPath = '';
            }
        }
        const checkAppleRootCertificate = (appleOption) => {
            try {
                // 导入 根证书 文件
                exeSync(`security import "${appleOption.ROOT_CER_FILE}"`);
            }
            catch (err) {
                if (!err.message.includes('already exists')) {
                    this.logger.warn(`${getLogTag('checkAppleRootCertificate')} 根证书导入失败: ${err.stack}`);
                    return false;
                }
            }
            return true;
        }
        const checkCertificate = (appleOption) => {
            try {
                // 导入 p12 文件
                exeSync(
                    `security import "${appleOption.P12_FILE}"` +
                    ` -k ~/Library/Keychains/login.keychain` +
                    ` -P "${appleOption.P12_PASSWORD}"` +
                    ` -A`
                );
            }
            catch (err) {
                // 判断错误信息不是重复导入
                if (!err.message.includes('already exists')) {
                    this.logger.warn(`${getLogTag('checkCertificate')} 证书导入失败: ${err.stack}`);
                    return false;
                }
            }
            return true;
        }

        // 判断是否有 xcodebuild, 如果没有就不用证书
        try {
            exeSync('which xcodebuild');
        }
        catch {
            this.logger.warn(`${getLogTag('updateXcodeEnvironment')} 不存在 Xcode 环境`);
            return;
        }
        // 配置目录文件环境
        updateWdaProjectPath(resourcesPath);
        if ('' === this.appleOption.wdaProjectPath) {
            return;
        }
        // 导入 苹果开发者证书 及 根目录证书
        let reallyUsefulTeam = [];
        for (let team of this.appleOption.USEFUL_TEAM) {
            this.appleOption[team].CER_FILE = path.join(rootPath, this.appleOption[team].CER_FILE);
            this.appleOption[team].P12_FILE = path.join(rootPath, this.appleOption[team].P12_FILE);
            this.appleOption[team].P8_FILE = path.join(rootPath, this.appleOption[team].P8_FILE);
            this.appleOption[team].ROOT_CER_FILE = path.join(rootPath, this.appleOption[team].ROOT_CER_FILE);
            if (!(checkAppleRootCertificate(this.appleOption[team]) && checkCertificate(this.appleOption[team]))) {
                this.logger.error(`${getLogTag('updateXcodeEnvironment')} team ${team} 证书导入失败`);
                delete this.appleOption[team];
            }
            else {
                reallyUsefulTeam.push(team);
            }
        }
        this.logger.info(`${getLogTag('updateXcodeEnvironment')} 当前有效 Team 为: ${JSON.stringify(reallyUsefulTeam)}`);
        this.appleOption.USEFUL_TEAM = reallyUsefulTeam;
        this.gotCertificate = 0 !== reallyUsefulTeam.length;
    }


    /*
     * --------------------
     * 本地工具基础初始化相关
     * --------------------
     */
    async registerLazyOne() {
        if (null !== this.lazyOneId) {
            return;
        }
        // 获取 MAC 信息
        let { networkList } = getNetwork();
        if (0 === networkList.length) {
            throw new Error('获取网络信息失败');
        }
        let mac = networkList[0].mac;
        // 获取是否之前有过注册信息
        const getLazyOneId = async () => {
            let res = await cloudServerRequest({
                baseURL: global.deviceAddress,
                path: '/core/device/device/query',
                body: {
                    udid: mac
                },
                headers: global.superHeader,
                method: 'POST'
            });
            return res;
        };
        // 注册 LazyOne 设备
        const register = async () => {
            // 上传注册
            let res = await cloudServerRequest({
                baseURL: global.deviceAddress,
                path: '/core/device/device/register',
                body: {
                    type: 4,
                    udid: mac
                },
                headers: global.superHeader,
                method: 'POST'
            });
            if (0 !== res.code) {
                throw new Error(`LazyOne 工具注册失败 ${res.msg}`);
            }
            return res.data;
        };
        let registerRes = await getLazyOneId();
        if (0 === registerRes.code) {
            if (registerRes.data.deviceList.length > 0) {
                this.lazyOneId = registerRes.data.deviceList[0].deviceId;
            }
            else {
                this.logger.info(`${getLogTag('registerLazyOne')} 发现新 LazyOne 工具, 进行注册`);
                this.lazyOneId = (await register()).deviceId;
            }
        }
        else {
            throw new Error(`获取 LazyOne 工具注册信息失败 ${registerRes.msg}`);
        }
        this.logger.info(`${getLogTag('registerLazyOne')} LazyOne 工具完成注册 id: ${this.lazyOneId}`);
    }

    getLazyOneId() {
        return this.lazyOneId;
    }

    async updateWorkMode(type) {
        if (![1, 2].includes(type)) {
            throw new Error(`非法的工具模式: ${type}`);
        }
        // 优先默认机房模式开启云端策略任务拉取
        let cloudServerMode = 2 === type && IS_LOCALTOOL ? 2 : 1;
        fs.writeFileSync(`${SYS_PROFILE_DIR}/mode.json`, JSON.stringify({ workMode: type, cloudServerMode, dorkingMode: this.dorkingMode }));
        this.mode = type;
        this.cloudServerMode = cloudServerMode;
    }

    getWorkMode() {
        return this.mode;
    }

    async updateDorkingMode(type) {
        if (![1, 2].includes(type)) {
            throw new Error(`非法的云控模式: ${type}`);
        }
        fs.writeFileSync(
            `${SYS_PROFILE_DIR}/mode.json`,
            JSON.stringify({ workMode: this.mode, cloudServerMode: this.cloudServerMode, dorkingMode: type })
        );
        this.dorkingMode = type;
    }

    getDorkingMode() {
        return this.dorkingMode;
    }

    async initWorkMode(workMode, cloudServerMode, dorkingMode) {
        fs.writeFileSync(
            `${SYS_PROFILE_DIR}/mode.json`,
            JSON.stringify({ workMode, cloudServerMode, dorkingMode })
        );
        this.mode = workMode;
        this.cloudServerMode = cloudServerMode;
        this.dorkingMode = dorkingMode;
    }


    /*
     * --------------------
     * 设备地图相关
     * --------------------
     */
    async _updateFrontDeviceMap() {
        let android = [];
        for (let id in this.deviceMap.android) {
            let { phoneId, brand, marketName, nickModel, status, statusMsg, statusStage, taskId } =
                this.deviceMap.android[id];
            android.push({
                deviceType: 'Android',
                deviceId: id,
                phoneId,
                brand,
                marketName,
                nickModel,
                status,
                statusMsg,
                statusStage,
                taskId
            });
        }
        let iOS = [];
        for (let id in this.deviceMap.iOS) {
            let { phoneId, brand, marketName, nickModel, status, statusMsg, statusStage, taskId } =
                this.deviceMap.iOS[id];
            iOS.push({
                deviceType: 'iOS',
                deviceId: id,
                phoneId,
                brand,
                marketName,
                nickModel,
                status,
                statusMsg,
                statusStage,
                taskId
            });
        }
        let deviceMap = { android, iOS };
        await this.callFront('device.update', deviceMap);
        await this.sendToFront('device.maps', deviceMap);
        fs.writeFileSync(`${DEVICE_PROFILE_DIR}/device.json`, JSON.stringify(deviceMap, null, 4));
    }

    setDeviceMap(newDeviceMap) {
        this.deviceMap = newDeviceMap;
    }

    getDeviceMap(type) {
        type = getDeviceType(type);
        if ('Android' === type) {
            return this.deviceMap.android;
        }
        else if ('iOS' === type) {
            return this.deviceMap.iOS;
        }
        else {
            throw new Error(`不支持该类型设备: ${type}`);
        }
    }

    getDevice(type, id) {
        let deviceMap = this.getDeviceMap(type);
        if (deviceMap[id]) {
            return deviceMap[id];
        }
        else {
            throw new ConfigError('未连接设备', '请通过 USB 连接设备');
        }
    }

    isDeviceRegister(type, id) {
        return undefined !== this.getDeviceMap(type)[id];
    }


    /*
     * --------------------
     * 设备云端 phoneId 相关
     * --------------------
     */
    updateDevicePhoneId(type, id, phoneId) {
        let device = this.getDevice(type, id);
        device.phoneId = phoneId;
    }

    getLazyOneId() {
        return this.lazyOneId;
    }

    getPhoneId(type, id) {
        let device = this.getDevice(type, id);
        let phoneId = device.phoneId;
        if (0 === phoneId) {
            throw new Error('设备未注册不可用, 请联系机房人员进行注册');
        }
        return phoneId;
    }

    updateDeviceProxyEnabled(type, id, proxyEnabled) {
        let device = this.getDevice(type, id);
        device.proxyEnabled = proxyEnabled;
    }

    getDeviceInfoByPhoneId(phoneId) {
        let result = { deviceFind: false };
        const findDeviceByType = type => {
            let deviceMap = this.getDeviceMap(type);
            for (let id in deviceMap) {
                if (phoneId === deviceMap[id].phoneId) {
                    result.deviceFind = true;
                    result.type = type;
                    result.id = id;
                    return true;
                }
            }
            return false;
        }
        findDeviceByType('iOS');
        false === result.deviceFind ? findDeviceByType('Android') : null;
        return result;
    }


    /*
     * --------------------
     * 设备初始化相关
     * --------------------
     */
    async registerDevice({ type, id, brand, marketName, nickModel, osVersion }) {
        let deviceMap = this.getDeviceMap(type);
        if (undefined !== deviceMap[id]) {
            deviceMap[id].brand = brand;
            deviceMap[id].marketName = marketName;
            deviceMap[id].nickModel = nickModel;
            deviceMap[id].osVersion = osVersion;
            deviceMap[id].phoneId = 0;
            deviceMap[id].proxyEnabled = true;
            deviceMap[id].status = 0;
            deviceMap[id].abnormalTime = null;
            deviceMap[id].statusMsg = '';
            deviceMap[id].statusStage = deepcopy(STATUS_STAGE);
            deviceMap[id].needCancel = false;
            deviceMap[id].handler = null;
            deviceMap[id].dom = null;
        }
        else {
            deviceMap[id] = {
                type,
                id,
                brand,
                marketName,
                nickModel,
                osVersion,
                status: 0, // 0 未初始化；1 初始化中；2 空闲；3 繁忙；4 异常；5 离线；6 离线中
                statusMsg: '',
                abnormalTime: null,
                statusStage: deepcopy(STATUS_STAGE),
                taskId: 0, // 占据设备的任务 ID
                phoneId: 0,
                proxyEnabled: true,
                needCancel: false,
                handler: null,
                dom: null
            };
        }
        this.logger.info(
            `${getLogTag('registerDevice')} [type:${type}] [id:${id}] [brand:${brand}] [marketName:${marketName}]`);
        await this._updateFrontDeviceMap();
        await this.sendToFront('device.connect', { deviceType: type, deviceId: id });
        return deviceMap[id];
    }

    async initDevice(type, id, options = {}, times = 1) {
        const logTag = getLogTag('initDevice');
        let device = null;
        let status = 0;
        try {
            device = this.getDevice(type, id);
            status = device.status;
            device.status = 1;
            await this._updateFrontDeviceMap();

            if (device.handler && device.handler.close) {
                this.logger.info(`${logTag} [type:${device.handler.type}] [id:${device.handler.id}] ${times}st close start`);
                await device.handler.close();
                this.logger.info(`${logTag} [type:${device.handler.type}] [id:${device.handler.id}] ${times}st close over`);
                device.handler = null;
            }

            if (device.handler && 'iOS' === device.handler.type) {
                options.screenSize = device.handler.handler.screenSize;
            }

            if ('iOS' === type) {
                options.appleOption = this.appleOption;
                options.appleOption.useXcode = this.gotCertificate;
            }

            this.logger.info(`${logTag} [type:${type}] [id:${id}] ${times}st launch start`);
            device.handler = await automator.launch(id, type, options);
            this.logger.info(`${logTag} [type:${type}] [id:${id}] ${times}st launch over`);

            device.dom = new Dom({
                device: device.handler,
                worker: this
            });

            if (0 === status || 1 === status || 4 === status || 5 === status || 6 === status) {
                device.status = 2;
            }
            else {
                device.status = status;
            }
            await this._updateFrontDeviceMap();
            return device;
        }
        catch (err) {
            if ('ConfigError' === err.type) {
                device.status = 4;
                device.statusMsg = err.message;
                await this._updateFrontDeviceMap();
                await this.sendToFront('notification.warn', {
                    title: '配置异常',
                    desc: err.message,
                    detail: err.detail
                });
                throw err;
            }
            else {
                this.logger.warn(`${logTag} [type:${type}] [id:${id}] ${times}st ${err.message}`);
                if (times > 3) {
                    device.status = 4;
                    device.statusMsg = `重试 3 次初始化失败: ${err.message}`;
                    await this._updateFrontDeviceMap();
                    await this.sendToFront('notification.error', {
                        title: '设备异常',
                        desc: '设备初始化失败',
                        detail: '请联系 LazyOne 开发者'
                    });
                    throw err;
                }
                else {
                    device.status = status;
                    return await this.initDevice(type, id, options, times + 1);
                }
            }
        }
    }

    async pushQAMateToDevice(type, id) {
        let device = this.getDevice(type, id);
        try {
            if ('android' === device.type.toLowerCase()) {
                await device.handler.handler.adb.shell(
                    device.id,
                    `echo ${device.phoneId} > /data/local/tmp/qamate${'product' === ENV ? '' : `-${ENV}`}.txt`
                );
                await device.handler.handler.adb.shell(
                    device.id,
                    'pm grant com.baidu.searchbox android.permission.PACKAGE_USAGE_STATS'
                );
            }
        }
        catch (err) {
            this.logger.warn(
                `${getLogTag('pushQAMateToDevice')} 设备 ${id} 推送 QAMate 文件失败 ${err.message}`
            );
        }
    }

    async initProxy(type, id) {
        let device = this.getDevice(type, id);
        await this.openSubProcess({
            name: `proxy_${device.phoneId}`,
            path: `${__dirname}/../proxy/index.js`
        }, `${PROXY_PROFILE_DIR}/${device.phoneId}_${id}.json`);
        let { ip, port } = await this.send(`proxy_${device.phoneId}`, 'getProxy');
        let res = await cloudServerRequest({
            baseURL: global.deviceAddress,
            path: '/core/device/proxy/setPort',
            body: {
                deviceId: device.phoneId,
                proxyPort: port
            },
            headers: global.superHeader,
            method: 'POST'
        });
        if (0 !== res.code) {
            throw new Error(`代理信息上报云端失败 ${res.msg}`);
        }
        return { ip, port };
    }

    async settingProxyToPac(type, id, needClear = false) {
        const getProxyPacSetting = async phoneId => {
            let res = await cloudServerRequest({
                baseURL: global.deviceAddress,
                path: `/core/device/proxy/pac/${phoneId}`,
                headers: { 'Content-Type': 'application/x-ns-proxy-autoconfig' },
                method: 'GET'
            });
            if (res.includes('DIRECT') || '' === res) {
                return { isEnable: false, proxy: '' };
            }
            return {
                isEnable: true,
                proxy: res.split('PROXY ')[1].split('"')[0]
            };
        };

        let { phoneId, handler: device } = this.getDevice(type, id);
        // 获取底层设定
        let proxySetting = await device.handler.adb.getProxySetting(device.id);
        // 获取 PAC 文件判断是否有代理
        let pacSetting = needClear ? { isEnable: false, proxy: '' } : await getProxyPacSetting(phoneId);
        // 判断是否需要修改
        if (
            // 如果本身设定就有问题就无脑直接改
            (proxySetting.http.isEnable !== proxySetting.https.isEnable) ||
            // 如果设定和 PAC 不一样就直接改
            (proxySetting.http.isEnable !== pacSetting.isEnable) ||
            // 如果代理启用，但是设定不同或与 PAC 不同就直接改
            (
                true === proxySetting.http.isEnable &&
                (
                    proxySetting.http.proxy !== proxySetting.https.proxy ||
                    proxySetting.http.proxy !== pacSetting.proxy
                )
            )
        ) {
            this.logger.info(
                `${getLogTag('settingProxy')} 检测设备 ${id} phoneId ${phoneId} 的代理设置不同` +
                ` 当前设置 ${JSON.stringify(proxySetting)} PAC 设置 ${JSON.stringify(pacSetting)}`
            );
            if (true === pacSetting.isEnable) {
                await device.handler.adb.setProxySetting(device.id, pacSetting.proxy);
            }
            else {
                await device.handler.adb.delProxySetting(device.id);
            }
            return false;
        }
        return true;
    }

    async autoCheckProxy(type, id) {
        let device = this.getDevice(type, id);
        let proxyName = `proxy_${device.phoneId}`;
        let checkAddress = `https://www.baidu.com/s?wd=qamateProxyName_${proxyName}`;
        if (device.handler) {
            if ('ios' === device.type.toLowerCase()) {
                await device.handler.scheme(checkAddress);
            }
            else if ('android' === device.type.toLowerCase()) {
                await this.settingProxyToPac(type, id, 2 !== this.mode);
                let { ip, port } = await this.send(`proxy_${device.phoneId}`, 'getProxy');
                await device.handler.request(checkAddress, { proxy: `${ip}:${port}` });
            }
        }
    }

    async offlineAllDevice() {
        const logTag = getLogTag('offlineAllDevice');
        const closeDevice = async (type, id) => {
            try {
                let device = this.getDevice(type, id);
                if (device.handler && device.handler.close) {
                    await device.handler.close();
                }
                await this.closeSubProcess(`proxy_${device.phoneId}`, 0);
            }
            catch (err) {
                this.logger.warn(`${logTag} 设备 ${type}-${id} 关闭失败 ${err.stack}`);
            }
        }
        // 妥善的关闭所有设备
        for (let id in this.deviceMap.android) {
            await closeDevice('Android', id);
        }
        for (let id in this.deviceMap.iOS) {
            await closeDevice('iOS', id);
        }
    }

    async offlineDevice(type, id) {
        const logTag = getLogTag('offlineDevice');
        const updateOfflineStatus = async (type, id) => {
            let device = this.getDevice(type, id);
            device.status = 5; // 离线
            device.statusStage = deepcopy(STATUS_STAGE);
            this.logger.info(`${logTag} ${type}-${id}`);
            await this._updateFrontDeviceMap();
            await this.sendToFront('device.disconnect', { deviceType: type, deviceId: id });
        };
        let device = this.getDevice(type, id);
        if (1 === device.status || 6 === device.status) {
            this.logger.warn(`${logTag} 设备 ${type}-${id} 初始化/离线中，无法离线`);
            return false;
        }
        else if (5 === device.status) {
            this.logger.warn(`${logTag} 设备 ${type}-${id} 已离线`);
            return false;
        }
        device.status = 6; // 离线中
        // 强制 10 秒必须要离线
        let timeout = setTimeout(async () => await updateOfflineStatus(type, id), 10000);
        try {
            // 子进程可能会关闭失败
            await this.closeSubProcess(`proxy_${device.phoneId}`, 0);
        }
        catch { }
        finally {
            if (device.handler && device.handler.close) {
                await device.handler.close();
            }
            // 清空定时器
            clearTimeout(timeout);
            await updateOfflineStatus(type, id);
        }
        return true;
    }


    /*
     * --------------------
     * 设备状态管理相关
     * --------------------
     */
    async updateDeviceStatusStage(type, id, stage) {
        let device = this.getDevice(type, id);
        device.statusStage = stage;
        await this._updateFrontDeviceMap();
        await this.sendToFront(
            'device.update',
            { deviceType: type, deviceId: id, deviceStatus: device.status, statusStage: device.statusStage }
        );
    }

    async abnormalDevice(type, id, msg) {
        let device = this.getDevice(type, id);
        device.status = 4;
        device.statusMsg = msg;
        device.abnormalTime = currentTimestamp();
        await this._updateFrontDeviceMap();
    };


    /*
     * --------------------
     * 设备空闲繁忙管理相关
     * --------------------
     */
    async checkDeviceFree(type, id) {
        const logTag = getLogTag('checkDeviceFree');
        const db = worker.getDb('replay');
        // 从 DB 里再次判断。该设备有没有本地任务正在执行中
        const checkDeviceFromLocalTask = async () => {
            let deviceTasks = await db.query('plan',
                { id: 'planId' },
                {
                    status: 2,
                    is_del: 0,
                    device_id: id,
                    device_type: type
                }
            );
            if (0 === deviceTasks.length) {
                return true;
            }
            return deviceTasks[0].planId;
        };
        const checkDeviceFromCloudTask = phoneId => (
            undefined === this.workingCloudTask[phoneId.toString()] ?
                true :
                this.workingCloudTask[phoneId.toString()]
        );
        const checkDeviceFromDebugTask = phoneId => (
            undefined === this.workingDebugTask[phoneId.toString()] ?
                true :
                this.workingDebugTask[phoneId.toString()]
        );

        try {
            let newType = getDeviceType(type);
            let device = this.getDevice(newType, id);
            // 设备繁忙
            if (2 !== device.status) {
                return false;
            }
            // 设备空闲, 再次判断是否有任务
            let realityTaskId = await checkDeviceFromLocalTask();
            if (true === realityTaskId) {
                // 如果没有 phoneId 不需要检查云端任务
                if (!device.phoneId) {
                    return true;
                }
                realityTaskId = checkDeviceFromCloudTask(device.phoneId);
                if (true === realityTaskId) {
                    realityTaskId = checkDeviceFromDebugTask(device.phoneId);
                    if (true === realityTaskId) {
                        return true;
                    }
                }
            }
            device.status = 3;
            device.taskId = realityTaskId;
            await this._updateFrontDeviceMap();
            this.logger.info(`${logTag} [type:${newType}] [id:${id}] [task:${realityTaskId}] 补充繁忙绑定`);
            return false;
        }
        catch (err) {
            this.logger.error(`${logTag} [type:${type}] [id:${id}] 检查空闲失败 ${err.stack}`);
            return false;
        }
    }

    async occupyDevice(type, id, taskId) {
        const logTag = getLogTag('occupyDevice');
        try {
            let newType = getDeviceType(type);
            let device = this.getDevice(newType, id);
            try {
                await device.handler.checkAvailable();
            }
            catch (err) {
                this.logger.error(`${logTag} [type:${newType}] [id:${id}] 设备未连接, 占用失败 ${err.stack}`);
                throw new Error('设备未连接');
            }
            if (await this.checkDeviceFree(type, id)) {
                device.status = 3;
                device.taskId = taskId;
                await this._updateFrontDeviceMap();
                this.logger.info(`${logTag} [type:${newType}] [id:${id}] [task:${taskId}]`);
                return true;
            }
            return false;
        }
        catch (err) {
            this.logger.error(`${logTag} [type:${type}] [id:${id}] [task:${taskId}] 占用设备失败 ${err.stack}`);
            return false;
        }
    }

    async freeDevice(type, id, taskId = 0) {
        const logTag = getLogTag('freeDevice');
        this.logger.info(`${logTag} 释放设备开始: task-${taskId} ${type}-${id}`);
        try {
            let device = this.getDevice(type, id);
            if (0 === taskId || device.taskId === taskId) {
                // 0 未初始化；1 初始化中；2 空闲；3 繁忙；4 异常；5 离线；6 离线中
                if (3 === device.status) {
                    device.status = 2; // 空闲
                }
                device.taskId = 0;
                this.cancelDeviceStatus(type, id, false);
                await this._updateFrontDeviceMap();
                this.logger.info(`${logTag} 释放设备完成: task-${taskId} ${type}-${id}`);
                return true;
            }
            return false;
        }
        catch (err) {
            return false;
        }
    }

    occupyCloudTask(phoneId, taskId) {
        this.workingCloudTask[phoneId.toString()] = taskId;
    }

    freeCloudTask(phoneId) {
        delete this.workingCloudTask[phoneId.toString()];
        let res = this.getDeviceInfoByPhoneId(phoneId);
        if (res.deviceFind) {
            this.cancelDeviceStatus(res.type, res.id, false);
        }
    }

    occupyDebugTask(phoneId, taskId) {
        this.workingDebugTask[phoneId.toString()] = taskId;
    }

    freeDebugTask(phoneId) {
        delete this.workingDebugTask[phoneId.toString()];
        let res = this.getDeviceInfoByPhoneId(phoneId);
        if (res.deviceFind) {
            this.cancelDeviceStatus(res.type, res.id, false);
        }
    }


    /*
     * --------------------
     * 设备执行相关
     * --------------------
     */
    async runSingleStep({
        deviceType,
        deviceId,
        step,
        checkStatus = true,
        needDeviceInfo = true,
        useDomCache = false,
        fishLogInfo = {},
        paramsList = [],
        recordTag = undefined,
        needCompleteAction = true,
        taskInfo = {}
    }) {
        let device = await this.getDevice(deviceType, deviceId);
        // 判断设备是否可用
        let acceptStatus = checkStatus ? [2] : [2, 3];
        if (!acceptStatus.includes(device.status)) {
            throw new Error('设备不处于空闲状态');
        }
        // 执行用例
        let { screenshot = '', screenSize = {}, rect = {}, extra = {} } =
            await stepRun({
                device: device.handler,
                dom: device.dom,
                stepInfo: step,
                worker: this,
                needDeviceInfo,
                useDomCache,
                fishLogInfo,
                paramsList,
                recordTag,
                needCompleteAction,
                taskInfo
            });
        return { screenshot, rect, screenSize, extra };
    }

    async clearSysAlert({ deviceType, deviceId, checkStatus = true, recordTag = null }) {
        let device = await this.getDevice(deviceType, deviceId);
        // 判断设备是否可用
        let acceptStatus = checkStatus ? [2] : [2, 3];
        if (!acceptStatus.includes(device.status)) {
            throw new Error('设备不处于空闲状态');
        }
        // 清理设备的系统弹窗
        await clearDeviceAlert({ device: device.handler, recordTag, worker: this });
    }

    async commonAlertClear({ deviceType, deviceId, params = {}, checkStatus = true, recordTag = null }) {
        let device = await this.getDevice(deviceType, deviceId);
        // 判断设备是否可用
        let acceptStatus = checkStatus ? [2] : [2, 3];
        if (!acceptStatus.includes(device.status)) {
            throw new Error('设备不处于空闲状态');
        }
        // 清理设备的系统弹窗
        await clearCommonAlert({ device: device.handler, params, recordTag, worker: this });
    }

    async resetDeviceUIStatus({ deviceType, deviceId, checkStatus = true }) {
        let device = await this.getDevice(deviceType, deviceId);
        // 判断设备是否可用
        let acceptStatus = checkStatus ? [2] : [2, 3];
        if (!acceptStatus.includes(device.status)) {
            throw new Error('设备不处于空闲状态');
        }
        try {
            // 让设备退出所有东西回到桌面
            if ('Android' === deviceType) {
                for (let i = 1; i <= 5; i++) {
                    await device.handler.back();
                    await delay(100);
                }
            }
            await device.handler.home();
            // 重置键盘回非 ADB 形态
            if ('Android' === deviceType) {
                let keyboardList = await device.handler.handler.adb.getKeyboardList(deviceId);
                let targetKeyboard = '';
                for (let keyboard of keyboardList.split('\n')) {
                    if (
                        !keyboard.includes('com.android.adbkeyboard/.AdbIME')
                        && !keyboard.includes('com.github.uiautomator/.FastInputIME')
                    ) {
                        targetKeyboard = keyboard;
                        break;
                    }
                }
                if ('' !== targetKeyboard) {
                    await device.handler.handler.adb.changeKeyboard(deviceId, targetKeyboard);
                }
            }
        }
        catch (err) {
            this.logger.warn(`${getLogTag('resetDeviceUIStatus')} 重置设备 UI 状态失败 ${err.stack}`)
        }
    }


    /*
     * --------------------
     * 单用例调试相关
     * --------------------
     */
    async generateDebugCase({
        deviceType,
        deviceId,
        caseInfo,
        paramsList,
        startStepId,
        sysAlertClear,
        needFishLog
    }) {
        let debugCaseTaskId = parseInt(`${currentTimestamp(true)}${Math.floor((Math.random() * 10) + 1)}`, 10);
        if (!await this.occupyDevice(deviceType, deviceId, debugCaseTaskId)) {
            throw new Error('设备不处于空闲状态');
        }
        let phoneId = this.getPhoneId(deviceType, deviceId);
        await worker.occupyDebugTask(phoneId, debugCaseTaskId);
        runDebugTask({
            worker: this,
            deviceType,
            deviceId,
            phoneId,
            caseInfo,
            paramsList,
            startStepId,
            sysAlertClear,
            needFishLog,
            taskId: debugCaseTaskId
        });
        return { taskId: debugCaseTaskId };
    }

    cancelDebugTask({ deviceType, deviceId }) {
        let phoneId = this.getPhoneId(deviceType, deviceId);
        let taskId = this.workingDebugTask[phoneId.toString()];
        if (undefined !== taskId) {
            this.cancelTaskList.push(taskId);
            this.cancelDeviceStatus(deviceType, deviceId, true);
        }
    }

    isDebugTaskCancel(taskId) {
        return this.cancelTaskList.includes(taskId);
    }

    cancelDeviceStatus(deviceType, deviceId, cancelStatus) {
        let device = this.getDevice(deviceType, deviceId);
        device.needCancel = cancelStatus;
    }

    getDeviceCancelStatus(deviceType, deviceId) {
        return (this.getDevice(deviceType, deviceId)).needCancel;
    }


    /*
     * --------------------
     * 设备录制操作步骤相关
     * --------------------
     */
    async startRecordAction({ deviceType, deviceId }) {
        if ('android' !== deviceType.toLowerCase()) {
            return;
        }
        let device = await this.getDevice(deviceType, deviceId);
        if (2 !== device.status) {
            throw new Error('设备不处于空闲状态');
        }
        let recordDir = `${CACHE_DEVICE_SCREENSHOT_DIR}/${device.id}_${currentTimestamp()}_${uuidv4()}`;
        mkdir(recordDir);
        await device.handler.recordFrames(recordDir);
        await device.handler.recordAction();
    }

    async stopRecordAction({ deviceType, deviceId, caseNodeId }) {
        if ('android' !== deviceType.toLowerCase()) {
            return;
        }
        let device = await this.getDevice(deviceType, deviceId);
        // 结束录制
        let recordRes = await device.handler.closeRecordFrames(true);
        let actionRes = await device.handler.closeRecordAction();
        // 缓存文件
        let recordDir = `${CACHE_DEVICE_SCREENSHOT_DIR}/${device.id}_${currentTimestamp()}_${uuidv4()}`;
        mkdir(recordDir);
        fs.writeFileSync(`${recordDir}/action.json`, JSON.stringify(actionRes));
        for (let item of recordRes.frames) {
            let isAvailable = false;
            for (let action of actionRes) {
                if (
                    (item.timestamp >= action.startTime && item.timestamp <= action.endTime)
                    || Math.abs(item.timestamp - action.startTime) < 100
                    || Math.abs(item.timestamp - action.endTime) < 100
                ) {
                    isAvailable = true;
                    break;
                }
            }
            if (isAvailable) {
                fs.copyFileSync(`${recordRes.storeDir}/${item.fileName}`, `${recordDir}/${item.fileName}`);
            }
            fs.unlinkSync(`${recordRes.storeDir}/${item.fileName}`);
        }
        // 上传压缩包
        let zipName = `${device.id}_${currentTimestamp()}_${uuidv4()}.tar.gz`;
        let zipFile = `${CACHE_FILE_DIR}/${zipName}`;
        await zip(zipFile, recordDir);
        let bosLink = await uploadFileAndDelete({
            filePath: zipFile,
            bosPath: `/lazy-one/${zipName}`
        });
        // 同步云端
        await cloudServerRequest({
            baseURL: global.managerAddress,
            path: '/core/case/node/update',
            headers: global.superHeader,
            body: {
                caseNodeIdList: [caseNodeId],
                linkList: [{
                    name: '用例操作链路',
                    path: bosLink
                }]
            },
            method: 'POST'
        });
    }


    /*
     * --------------------
     * 设备外部操作管理
     * --------------------
     */
    async domCall(action, { type, id }, ...params) {
        let { status, dom } = this.getDevice(type, id);
        if (![2, 3].includes(status)) {
            throw new Error('设备不处于空闲状态');
        }
        return await dom[action](...params);
    }

    async deviceCall(action, { type, id }, ...params) {
        let { status, handler } = this.getDevice(type, id);
        if (![2, 3].includes(status)) {
            throw new Error('设备不处于空闲状态');
        }
        return await handler[action](...params);
    }
}

const init = async ({ name, fatherName }, { resourcePath }) => {
    worker = new DeviceWorker({ name, fatherName }, { resourcePath });
    await worker._updateFrontDeviceMap();
    await worker.ready();
}

const get = () => worker;

module.exports = { init, get };