/**
 * @create 王家麒@2023.08.29
 */
const LazyBaseWorker = require('../../lib/lazy-worker/LazyBaseWorker');
const { logger } = require('./logger');

let worker = null;

class TrafficWorker extends LazyBaseWorker {
    constructor({ name, fatherName }) {
        super({ name, fatherName, logger });
    }

    async closeWorker(code) {
        const closeProcess = async workerName => {
            try {
                // 有可能这个进程已经先行死亡
                if (undefined === this.workerMap[workerName]) {
                    return;
                }
                await this.closeSubProcess(workerName, code);
            }
            catch (err) {
                this.logger.error(`[${this.name}.close] ${workerName} 子进程关闭失败 ${err.stack}`);
            }
        }

        let needToLastClose = ['localServer', 'db'];
        // 关闭应用进程
        for (let subWorkerName of Object.keys(this.workerMap)) {
            if (needToLastClose.includes(subWorkerName)) {
                continue;
            }
            await closeProcess(subWorkerName);
        }
        // 关闭最后的依赖进程
        for (let subWorkerName of needToLastClose) {
            await closeProcess(subWorkerName);
        }
    }
};

const init = async ({ name, fatherName }, { resourcePath }) => {
    worker = new TrafficWorker({ name, fatherName });

    let promiseList = [];
    // 启动 数据库
    await worker.openSubProcess({
        name: 'db',
        path: `${__dirname}/../db/index.js`
    });

    // 启动 本地算法服务器
    promiseList.push(worker.openSubProcess({
        name: 'localServer',
        path: `${__dirname}/../server/index.js`
    }, resourcePath));

    // 启动 缓存读取
    promiseList.push(worker.openSubProcess({
        name: 'data',
        path: `${__dirname}/../data/index.js`
    }));
    promiseList.push(worker.openSubProcess({
        name: 'cache',
        path: `${__dirname}/../cache/index.js`
    }));

    // 启动 缓存同步
    promiseList.push(worker.openSubProcess({
        name: 'sync',
        path: `${__dirname}/../sync/index.js`
    }));

    // 启动 设备管理
    promiseList.push(worker.openSubProcess({
        name: 'device',
        path: `${__dirname}/../device/index.js`
    }, resourcePath));

    // 启动 空间清理
    promiseList.push(worker.openSubProcess({
        name: 'clean',
        path: `${__dirname}/../clean/index.js`
    }));

    // 启动 文件下载
    promiseList.push(worker.openSubProcess({
        name: 'file',
        path: `${__dirname}/../file/index.js`
    }));

    // 启动 任务上传
    promiseList.push(worker.openSubProcess({
        name: 'upload',
        path: `${__dirname}/../upload/index.js`
    }));

    // 启动 队列消费
    promiseList.push(worker.openSubProcess({
        name: 'kafka',
        path: `${__dirname}/../kafka/index.js`
    }));

    // 启动 UBC 点位记录进程
    promiseList.push(worker.openSubProcess({
        name: 'ubc',
        path: `${__dirname}/../ubc/index.js`
    }));


    // 等待所有子进程开启完毕
    Promise.all(promiseList).then(async () => {
        logger.info('LazyOne 启动成功');
        await worker.ready();
    }).catch(err => {
        logger.error(`LazyOne 启动失败 ${err.messgae} ${err.stack}`);
        throw err;
    });
}

const get = () => worker;

module.exports = { init, get };