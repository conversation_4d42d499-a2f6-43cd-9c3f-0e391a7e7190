/**
 * @create 王家麒@2022.05.20
 */
const sqlite3 = require('sqlite3');
const { time: { currentTimestamp } } = require('@baidu/bat-util');

const data = require('./data');
const { DB_DIR } = require('../config');

const AVAILABLE_DB = ['replay', 'case', 'file', 'knowledge'];

const dbMap = {};

const getDb = dbName => {
    if (!AVAILABLE_DB.includes(dbName)) {
        throw new Error(`不支持的DB: ${dbName}`);
    }
    const dbPath = `${DB_DIR}/${dbName}.db`;
    if (!dbMap[dbName]) {
        let handler = new sqlite3.Database(dbPath);
        handler.configure('busyTimeout', 6000);
        dbMap[dbName] = {
            handler,
            update: async (table, content, condition) => {
                let params = {};
                let set = [];
                for (let key in content) {
                    if (Object.prototype.hasOwnProperty.call(content, key)) {
                        params[`$set_${key}`] = content[key];
                        set.push(`\`${key}\` = $set_${key}`);
                    }
                }
                set = 'set ' + set.join(', ');

                let where = '';
                if ('object' === typeof condition) {
                    let whereList = [];
                    for (let key in condition) {
                        if (Object.prototype.hasOwnProperty.call(condition, key)) {
                            params[`$where_${key}`] = condition[key];
                            whereList.push(`\`${key}\` = $where_${key}`);
                        }
                    }
                    where = 'where ' + whereList.join(' and ');
                }
                else if ('string' === typeof condition) {
                    where = `where ${condition}`;
                }

                let sql = `update \`${table}\` ${set} ${where}`;
                return dbMap[dbName].run(sql, params);
            },
            insert: async (table, content) => {
                let columns = Object.keys(content).join('`, `');
                let newContent = {};
                for (let column in content) {
                    if (Object.prototype.hasOwnProperty.call(content, column)) {
                        newContent[`$${column}`] = content[column];
                    }
                }
                let values = Object.keys(newContent).join(', ');
                let sql = 'insert into `' + table + '` (`' + columns + '`) values (' + values + ')';
                return dbMap[dbName].run(sql, newContent);
            },
            query: async (table, column = {}, condition = {}, append = {}) => {
                let columnSql = '*';
                if ('object' === typeof column) {
                    let columns = [];
                    if (Array.isArray(column)) {
                        columns = column;
                    }
                    else {
                        for (let key in column) {
                            if (Object.prototype.hasOwnProperty.call(column, key)) {
                                columns.push(`${key} as \`${column[key]}\``);
                            }
                        }
                    }
                    if (columns.length) {
                        columnSql = columns.join(', ');
                    }
                }
                else if ('string' === typeof column) {
                    columnSql = column;
                }

                let newCondition = {};
                let whereSql = '';
                if ('object' === typeof condition) {
                    let conditions = [];
                    for (let key in condition) {
                        if (Object.prototype.hasOwnProperty.call(condition, key)) {
                            if (Array.isArray(condition[key])) {
                                conditions.push(`\`${key}\` ${condition[key][0]} $${key}`);
                                newCondition[`$${key}`] = condition[key][1];
                            }
                            else {
                                conditions.push(`\`${key}\` = $${key}`);
                                newCondition[`$${key}`] = condition[key];
                            }
                        }
                    }
                    if (conditions.length) {
                        whereSql = 'where ' + conditions.join(' and ');
                    }
                }
                else if ('string' === typeof condition) {
                    whereSql = 'where ' + condition;
                }



                let appends = [];
                if (append.orderBy) {
                    appends.push(`order by ${append.orderBy}`);
                }
                if (append.limit) {
                    appends.push(`limit ${append.limit}`);
                }
                if (append.offset) {
                    appends.push(`offset ${append.offset}`);
                }
                let appendSql = appends.join(' ');

                let sql = `select ${columnSql} from \`${table}\` ${whereSql} ${appendSql}`;
                return dbMap[dbName].all(sql, newCondition);
            },
            exec: async (...args) => new Promise((resolve, reject) => {
                handler.exec(...args, function (err) {
                    if (err) {
                        reject(err);
                    }
                    else {
                        resolve();
                    }
                });
            }),
            run: async (...args) => new Promise((resolve, reject) => {
                handler.run(...args, function (err) {
                    if (err) {
                        reject(err);
                    }
                    else {
                        resolve({
                            id: this.lastID,
                            changes: this.changes
                        });
                    }
                });
            }),
            all: async (...args) => new Promise((resolve, reject) => {
                handler.all(...args, (err, res) => {
                    if (err) {
                        reject(err);
                    }
                    else {
                        resolve(res);
                    }
                });
            }),
            close: async (...args) => handler.close(...args),
        };
    }
    return dbMap[dbName];
};

const init = async () => {
    const replayDb = getDb('replay');
    await replayDb.run('PRAGMA busy_timeout = 6000');
    const caseDb = getDb('case');
    await caseDb.run('PRAGMA busy_timeout = 6000');
    const fileDb = getDb('file');
    await fileDb.run('PRAGMA busy_timeout = 6000');
    const knowledgeDb = getDb('knowledge');
    await knowledgeDb.run('PRAGMA busy_timeout = 6000');

    // 兼容老 DB 数据
    await compatible();
    await replayDb.exec(data.get('db/init/replay.sql'));
    await caseDb.exec(data.get('db/init/case.sql'));
    await fileDb.exec(data.get('db/init/file.sql'));
    await knowledgeDb.exec(data.get('db/init/knowledge.sql'));
};

const compatible = async () => {
    const replayDb = getDb('replay');
    let replaySqlList = [
        'alter table `plan` add column `is_del` integer not null default 0',
        'alter table `plan` add column `cloud_id` integer not null default -1',
        'alter table `task` add column `cloud_status` integer not null default 0',
        'alter table `task` add column `extra_info` text not null default \'{}\'',
        'alter table `plan` add column `extra_info` text not null default \'{}\'',
        'alter table `task` add column `retry_times` integer not null default 1',
        'alter table `plan` add column `group_id` integer not null default -1',
    ];
    for (let sql of replaySqlList) {
        try {
            await replayDb.exec(sql);
        }
        catch { }
    }

    const caseDb = getDb('case');
    let caseSqlList = [];
    for (let sql of caseSqlList) {
        try {
            await caseDb.exec(sql);
        }
        catch { }
    }

    const fileDb = getDb('file');
    let fileSqlList = [
        // 这里有个大坑。。v2.5.0 版本用了 NULL，马上改了, 但是 SQLITE3 不允许直接改字段。。所以可能会有用户的不一致。使用时注意！！！
        // 'alter table `file` add column `md5` text null', 
        'alter table `file` add column `md5` text not null default \'\'',
    ];
    for (let sql of fileSqlList) {
        try {
            await fileDb.exec(sql);
        }
        catch { }
    }

    const knowledgeDb = getDb('knowledge');
    let knowledgeSqlList = [];
    for (let sql of knowledgeSqlList) {
        try {
            await knowledgeDb.exec(sql);
        }
        catch { }
    }
};

const timestamp = () => Math.round(currentTimestamp() / 1000);

module.exports = { init, getDb, timestamp };