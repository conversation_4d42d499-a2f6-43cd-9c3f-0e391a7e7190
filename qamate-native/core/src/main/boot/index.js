/**
 * @create 王家麒@2023.03.10
 */
const { time: { delay } } = require('@baidu/bat-util');
const { IS_DEBUG } = require('../../config');
const { logger } = require('../logger');

const loadUrl = async ({ url, mainWindow }) => {
    const loadUrlMain = (url, index) => new Promise((resolve, reject) => {
        mainWindow.webContents.loadURL(url)
            .then(() => {
                logger.info('loadURL 成功');
                resolve();
            })
            .catch(err => {
                logger.warn(`loadURL 异常，进行重试 ${index}th`);
                reject(new Error(`前端重启异常 ${err.message}`));
            });
    });
    for (let index = 1; index <= 3; index++) {
        try {
            await loadUrlMain(url, index);
            break;
        }
        catch (err) {
            if (3 === index) {
                throw err;
            }
            await delay(200);
        }
    }
};

const loadFile = async ({ path, mainWindow }) => {
    const loadFileMain = (path, index) => new Promise((resolve, reject) => {
        mainWindow.webContents.loadFile(path)
            .then(() => {
                logger.info('loadFile 成功');
                resolve();
            })
            .catch(err => {
                logger.warn(`loadFile 异常，进行重试 ${index}th`);
                reject(new Error(`前端重启异常 ${err.message}`));
            });
    });
    for (let index = 1; index <= 3; index++) {
        try {
            await loadFileMain(path, index);
            break;
        }
        catch (err) {
            if (3 === index) {
                throw err;
            }
            await delay(200);
        }
    }
};

const bootFront = mainWindow => new Promise(async (resolve) => {
    try {
        if (IS_DEBUG) {
            // 这
            await loadUrl({ url: 'http://127.0.0.1:8000', mainWindow });
        }
        else {
            await loadUrl({ url: global.defaultAddress, mainWindow });
        }
    }
    catch (err) {
        logger.error(`前端启动异常 ${err.stack}`);
    }
    finally {
        resolve();
    }
    // let undertakeFrontPath = `${__dirname}/../../../public/front/index.html`;
    // try {
    //     if (IS_DEBUG) {
    //         await loadUrl({ url: 'http://127.0.0.1:8000', mainWindow });
    //     }
    //     else {
    //         let profileJson = JSON.parse(fs.readFileSync(`${HOT_UPDATE_DIR}/profile.json`));
    //         if (true !== profileJson.front.getHotUpdate) {
    //             logger.info('暂无前端热更新，启动原始前端路径');
    //             await loadFile({ path: undertakeFrontPath, mainWindow });
    //         }
    //         else {
    //             logger.info(`前端启动路径为: ${profileJson.front.newPath}`);
    //             await loadUrl({ url: `file://${profileJson.front.newPath}`, mainWindow });
    //         }
    //     }
    // }
    // catch (err) {
    //     logger.warn(`前端启动异常，兜底启动原路径 ${err.stack}`);
    //     try {
    //         if (IS_DEBUG) {
    //             await loadUrl({ url: 'http://127.0.0.1:8000', mainWindow });
    //         }
    //         else {
    //             await loadFile({ path: undertakeFrontPath, mainWindow });
    //         }
    //     }
    //     catch {
    //         logger.error(`兜底启动异常 ${err.stack}`);
    //     }
    // }
    // finally {
    //     resolve();
    // }
});

module.exports = { bootFront, loadFile, loadUrl };