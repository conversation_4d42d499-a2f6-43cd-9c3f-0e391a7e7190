/**
 * @create 王家麒@2022.05.19
 */
const fs = require('fs');
const os = require('os');
const { file: { mkdir, rmdir } } = require('@baidu/bat-util');
const { IS_DEBUG, ENV } = require('./env');

const WORK_DIR = `${os.homedir}/.lazyOne`;
// 日志相关
const LOG_DIR = `${WORK_DIR}/log`; // ~/.lazyOne/log
// 下载文件相关
const FILE_DIR = `${WORK_DIR}/file`; // ~/.lazyOne/file
// 策略文件相关
const SERVER_DIR = `${WORK_DIR}/server`; // ~/.lazyOne/server
// 缓存图片相关
const IMAGE_DIR = `${WORK_DIR}/image`; // ~/.lazyOne/image
const IMAGE_NODE_DIR = `${IMAGE_DIR}/case`; // ~/.lazyOne/image/case
// 用例预渲染相关
const CASE_DIR = `${WORK_DIR}/case`; // ~/.lazyOne/case
const NODE_DIR = `${CASE_DIR}/node`; // ~/.lazyOne/case/node
// 配置文件相关
const PROFILE_DIR = `${WORK_DIR}/profile`; // ~/.lazyOne/profile
const SYS_PROFILE_DIR = `${PROFILE_DIR}/sys`; // ~/.lazyOne/profile/sys
const PROXY_PROFILE_DIR = `${PROFILE_DIR}/proxy`; // ~/.lazyOne/profile/proxy
const SERVER_PROFILE_DIR = `${PROFILE_DIR}/server`; // ~/.lazyOne/profile/server
const DEVICE_PROFILE_DIR = `${PROFILE_DIR}/device`; // ~/.lazyOne/profile/device
// 本地回放相关
const REPLAY_DIR = `${WORK_DIR}/replay`; // ~/.lazyOne/replay
// 本地数据库相关
const DB_DIR = `${WORK_DIR}/db`; // ~/.lazyOne/db
// 错误请求留存相关
const ERROR_DIR = `${WORK_DIR}/error`; // ~/.lazyOne/error
// 热更新相关
const HOT_UPDATE_DIR = `${IS_DEBUG ? `${__dirname}/../../public` : `${process.resourcesPath}/public`}/hot-update`;
const UPDATE_PACKAGE_DIR = `${HOT_UPDATE_DIR}/package`;

// 缓存相关
// 基本缓存目录, 不对外吐出
const CACHE_DIR = `${WORK_DIR}/cache`; // ~/.lazyOne/cache
const CACHE_DEVICE_DIR = `${CACHE_DIR}/device`; // ~/.lazyOne/cache/device
// 对外吐出的缓存目录
const CACHE_FILE_DIR = `${CACHE_DIR}/file`; // ~/.lazyOne/cache/file
const CACHE_DEVICE_SCREENSHOT_DIR = `${CACHE_DEVICE_DIR}/screenshot`; // ~/.lazyOne/cache/device/screenshot
const CACHE_DEVICE_FILE_DIR = `${CACHE_DEVICE_DIR}/file`; // ~/.lazyOne/cache/device/file

module.exports = {
    WORK_DIR,
    LOG_DIR,
    NODE_DIR,
    IMAGE_NODE_DIR,
    REPLAY_DIR,
    DB_DIR,
    ERROR_DIR,
    FILE_DIR,
    SERVER_DIR: `${SERVER_DIR}/${ENV}`,
    PROFILE: {
        SYS_PROFILE_DIR,
        PROXY_PROFILE_DIR,
        SERVER_PROFILE_DIR: `${SERVER_PROFILE_DIR}/${ENV}`,
        DEVICE_PROFILE_DIR
    },
    UPDATE: {
        HOT_UPDATE_DIR,
        UPDATE_PACKAGE_DIR
    },
    CACHE: {
        CACHE_FILE_DIR,
        CACHE_DEVICE_SCREENSHOT_DIR,
        CACHE_DEVICE_FILE_DIR
    },
    initWorkDir: () => {
        // 建立目录
        mkdir(WORK_DIR);
        mkdir(LOG_DIR);
        mkdir(REPLAY_DIR);
        mkdir(CASE_DIR);
        mkdir(NODE_DIR);
        mkdir(IMAGE_DIR);
        mkdir(IMAGE_NODE_DIR);
        mkdir(DB_DIR);
        mkdir(ERROR_DIR);
        mkdir(PROFILE_DIR);
        mkdir(SYS_PROFILE_DIR);
        mkdir(PROXY_PROFILE_DIR);
        mkdir(SERVER_PROFILE_DIR);
        mkdir(`${SERVER_PROFILE_DIR}/${ENV}`);
        mkdir(DEVICE_PROFILE_DIR);
        mkdir(FILE_DIR);
        mkdir(SERVER_DIR);
        mkdir(`${SERVER_DIR}/${ENV}`);
        mkdir(UPDATE_PACKAGE_DIR);

        // 重置缓存
        try {
            rmdir(CACHE_DIR);
        } catch { }
        mkdir(CACHE_DIR);
        mkdir(CACHE_FILE_DIR);
        mkdir(CACHE_DEVICE_DIR);
        mkdir(CACHE_DEVICE_SCREENSHOT_DIR);
        mkdir(CACHE_DEVICE_FILE_DIR);
    },
    compatibleWorkDir: () => {
        // 调整 PROFILE_DIR -> SYS_PROFILE_DIR
        if (fs.existsSync(`${PROFILE_DIR}/mode.json`)) {
            fs.copyFileSync(`${PROFILE_DIR}/mode.json`, `${SYS_PROFILE_DIR}/mode.json`);
            fs.unlinkSync(`${PROFILE_DIR}/mode.json`);
        }
        // 确保 热更新 profile 一定存在，如果不在的话用 profile-template.json 补上
        if (!fs.existsSync(`${HOT_UPDATE_DIR}/profile.json`)) {
            fs.copyFileSync(`${HOT_UPDATE_DIR}/profile-template.json`, `${HOT_UPDATE_DIR}/profile.json`);
        }
    }
};