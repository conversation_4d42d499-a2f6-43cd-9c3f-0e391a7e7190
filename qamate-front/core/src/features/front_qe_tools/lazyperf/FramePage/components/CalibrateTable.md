# CalibrateTable 组件

## 概述

CalibrateTable 是一个用于显示和管理性能测试校准数据的表格组件。它基于旧版本的 `detail-info.jsx` 重构而来，支持智能校准和人工校准的 TTI 计算、记录状态管理、以及废弃/恢复操作。

## 功能特性

### 1. 数据获取
- 自动调用 `/core/perf/speed/round/list` 接口获取数据
- 支持通过 props 直接传入数据列表
- 支持加载状态显示

### 2. TTI 计算
- **智能TTI**: 基于 `auto.lastFrameTimestamp - auto.firstFrameTimestamp` 计算
- **人工TTI**: 优先使用人工校准的时间戳，否则使用智能校准结果
- 支持异常情况检测（首帧大于尾帧）

### 3. 状态管理
- **校准中**: 智能校准未完成（`auto.firstFrameStatus === 0` 或 `auto.lastFrameStatus === 0`）
- **智能校准**: 仅完成智能校准
- **人工校准**: 已完成人工校准（`manual.firstFrameStatus === 1` 或 `manual.lastFrameStatus === 1`）
- **已废弃**: 记录被标记为无效（`isValid === 1`）

### 4. 操作功能
- **校准**: 触发 `onCalibrate` 回调函数
- **废弃**: 调用 `updateSpeedRound` 接口标记记录为无效，支持添加废弃备注
- **恢复**: 恢复已废弃的记录

## Props 参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| planId | number | 否 | - | 计划ID，用于获取数据 |
| caseNodeId | number | 否 | - | 用例节点ID，用于获取数据 |
| sceneId | number | 否 | - | 场景ID，用于获取数据 |
| recordList | array | 否 | - | 直接传入的记录列表，如果提供则不会调用接口 |
| onCalibrate | function | 否 | - | 校准操作回调函数，参数为当前记录对象 |
| checkMode | boolean | 否 | false | 是否启用检查模式，会添加检查列 |

## 使用示例

### 基本使用

```jsx
import CalibrateTable from './CalibrateTable';

const MyComponent = () => {
    const handleCalibrate = (record) => {
        console.log('开始校准记录:', record);
        // 打开校准弹窗或跳转到校准页面
    };

    return (
        <CalibrateTable
            planId={12}
            caseNodeId={123}
            sceneId={3}
            onCalibrate={handleCalibrate}
        />
    );
};
```

### 传入数据列表

```jsx
const recordList = [
    {
        recordSceneId: 123,
        isValid: 0,
        frameList: [...],
        correctDetail: {
            auto: { ... },
            manual: { ... }
        },
        // ... 其他字段
    }
];

return (
    <CalibrateTable
        recordList={recordList}
        onCalibrate={handleCalibrate}
        checkMode={true}
    />
);
```

## 接口数据格式

### 请求参数 (`/core/perf/speed/round/list`)

```json
{
    "planId": 12,
    "caseNodeId": 123,
    "sceneId": 3
}
```

### 响应数据格式

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "recordList": [
            {
                "recordSceneId": 123,
                "isUpload": 0,
                "frameList": [{"timestamp": 1752762044, "frame": "https://xxx"}],
                "stepRecord": [[{"name": "x", "timestamp": 1752762044}]],
                "stageList": [7, 10, 99],
                "correctDetail": {
                    "auto": {
                        "firstFrameStatus": 0,
                        "firstFrameIndex": 0,
                        "firstFrameTimestamp": 1752762044,
                        "lastFrameStatus": 0,
                        "lastFrameIndex": 0,
                        "lastFrameTimestamp": 1752762044
                    },
                    "manual": {
                        "firstFrameStatus": 0,
                        "firstFrameIndex": 0,
                        "firstFrameTimestamp": 1752762044,
                        "lastFrameStatus": 0,
                        "lastFrameIndex": 0,
                        "lastFrameTimestamp": 1752762044
                    }
                },
                "isValid": 0,
                "invalidComment": "",
                "updateUser": "",
                "updateTime": 1752762044
            }
        ]
    }
}
```

## 依赖的 API

### 1. getSpeedRoundList
- 路径: `/core/perf/speed/round/list`
- 方法: POST
- 用途: 获取实际执行列表任务信息

### 2. updateSpeedRound
- 路径: `/core/perf/speed/round/valid`
- 方法: POST
- 用途: 更新速度评测数据有效情况

## 注意事项

1. 如果同时提供了 `recordList` 和接口参数，优先使用 `recordList`
2. TTI 计算会处理异常情况（首帧大于尾帧），显示错误提示
3. 废弃操作支持添加备注，恢复操作会清空备注
4. 表格支持分页，默认每页显示 10 条记录
5. 所有操作都会更新本地状态，确保 UI 实时响应

## 迁移说明

从旧版本 `detail-info.jsx` 迁移到新版本的主要变化：

1. **组件化**: 从 dva model 模式改为独立的 React 组件
2. **API 调用**: 使用新的 API 接口和数据格式
3. **状态管理**: 使用 React hooks 替代 dva state
4. **Props 接口**: 提供更灵活的 props 配置
5. **代码结构**: 更清晰的函数组织和错误处理
