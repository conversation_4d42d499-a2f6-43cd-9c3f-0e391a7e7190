import React from 'react';
import { Card } from 'antd';
import CalibrateTable from './CalibrateTable';

/**
 * CalibrateTable 使用示例
 * 展示如何使用完善后的校准表格组件
 */
const CalibrateTableExample = () => {
    // 处理校准操作的回调函数
    const handleCalibrate = (record) => {
        console.log('开始校准记录:', record);
        // 这里可以打开校准弹窗或跳转到校准页面
        // 例如：打开 FrameViewer 组件进行帧校准
    };

    return (
        <div style={{ padding: '20px' }}>
            <Card title="校准表格示例" style={{ marginBottom: '20px' }}>
                <h3>使用方式 1: 通过 props 传入参数自动获取数据</h3>
                <CalibrateTable
                    planId={12}
                    caseNodeId={123}
                    sceneId={3}
                    onCalibrate={handleCalibrate}
                    checkMode={false}
                />
            </Card>

            <Card title="功能说明">
                <h3>主要功能：</h3>
                <ul>
                    <li><strong>数据获取</strong>: 自动调用 /core/perf/speed/round/list 接口获取数据</li>
                    <li><strong>TTI 计算</strong>: 
                        <ul>
                            <li>智能TTI: 基于 auto.lastFrameTimestamp - auto.firstFrameTimestamp</li>
                            <li>人工TTI: 优先使用人工校准的时间戳，否则使用智能校准结果</li>
                        </ul>
                    </li>
                    <li><strong>状态显示</strong>: 
                        <ul>
                            <li>校准中 (智能校准未完成)</li>
                            <li>智能校准 (仅完成智能校准)</li>
                            <li>人工校准 (已完成人工校准)</li>
                            <li>已废弃 (记录被标记为无效)</li>
                        </ul>
                    </li>
                    <li><strong>操作功能</strong>:
                        <ul>
                            <li>校准: 触发 onCalibrate 回调</li>
                            <li>废弃: 调用 updateSpeedRound 接口标记记录为无效</li>
                            <li>恢复: 恢复已废弃的记录</li>
                        </ul>
                    </li>
                    <li><strong>检查模式</strong>: 可选的检查列，用于质量检查流程</li>
                </ul>

                <h3>Props 参数：</h3>
                <ul>
                    <li><code>planId</code>: 计划ID</li>
                    <li><code>caseNodeId</code>: 用例节点ID</li>
                    <li><code>sceneId</code>: 场景ID</li>
                    <li><code>recordList</code>: 可选，直接传入记录列表</li>
                    <li><code>onCalibrate</code>: 校准操作回调函数</li>
                    <li><code>checkMode</code>: 是否启用检查模式</li>
                </ul>

                <h3>接口数据格式：</h3>
                <pre style={{ background: '#f5f5f5', padding: '10px', fontSize: '12px' }}>
{`{
  "code": 0,
  "msg": "success", 
  "data": {
    "recordList": [
      {
        "recordSceneId": 123,
        "isUpload": 0,
        "frameList": [{"timestamp": 1752762044, "frame": "https://xxx"}],
        "stepRecord": [[{"name": "x", "timestamp": 1752762044}]],
        "stageList": [7, 10, 99],
        "correctDetail": {
          "auto": {
            "firstFrameStatus": 0,
            "firstFrameIndex": 0, 
            "firstFrameTimestamp": 1752762044,
            "lastFrameStatus": 0,
            "lastFrameIndex": 0,
            "lastFrameTimestamp": 1752762044
          },
          "manual": {
            "firstFrameStatus": 0,
            "firstFrameIndex": 0,
            "firstFrameTimestamp": 1752762044, 
            "lastFrameStatus": 0,
            "lastFrameIndex": 0,
            "lastFrameTimestamp": 1752762044
          }
        },
        "isValid": 0,
        "invalidComment": "",
        "updateUser": "",
        "updateTime": 1752762044
      }
    ]
  }
}`}
                </pre>
            </Card>
        </div>
    );
};

export default CalibrateTableExample;
