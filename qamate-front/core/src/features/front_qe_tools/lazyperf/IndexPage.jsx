import { useEffect, useState } from 'react';
import { useNavigate } from 'umi';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import lazyperfModel from 'COMMON/models/lazyperfModel';
import { getQueryParams } from 'COMMON/utils/utils';
import TaskPage from './TaskPage';
import FramePage from './FramePage';
import ReportPage from './ReportPage';
import styles from './IndexPage.module.less';

function IndexPage(props) {
    const { currentSpace, perfPlanList } = props;
    const [curTask, setCurTask] = useState(null);
    const [loading, setLoading] = useState(false);
    const query = getQueryParams();
    const activedStage = query?.stage ?? 'task';

    // 获取当前任务
    useEffect(() => {
        if (!query?.planId) {
            return;
        }
        setCurTask(perfPlanList?.find((item) => item?.planId === +query?.planId));
    }, [query?.planId, perfPlanList]);

    return (
        <div className={styles.container}>
            {activedStage === 'task' && (
                <TaskPage curTask={curTask} loading={loading} setLoading={setLoading} />
            )}
            {activedStage === 'frame' && (
                <FramePage curTask={curTask} loading={loading} setLoading={setLoading} />
            )}
            {activedStage === 'report' && (
                <ReportPage curTask={curTask} loading={loading} setLoading={setLoading} />
            )}
        </div>
    );
}

export default connectModel([baseModel, lazyperfModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentModule: state.common.base.currentModule,
    perfPlanList: state.common.lazyperf.perfPlanList
}))(IndexPage);
