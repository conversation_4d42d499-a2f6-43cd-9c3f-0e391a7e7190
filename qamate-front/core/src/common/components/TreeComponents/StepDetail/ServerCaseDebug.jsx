import {useEffect, useState, useRef} from 'react';
import {message} from 'antd';
import classnames from 'classnames';
import {
    CodeSandboxOutlined
} from '@ant-design/icons';
import {v4} from 'uuid';
import {post} from 'COMMON/utils/requestUtils';
import {getQueryParams} from 'COMMON/utils/utils';
import {WS_DEBUG_BASICURL} from 'COMMON/config/baseUrl';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import styles from './StepDetail.module.less';

function ServerCaseDebug(props) {
    const {currentEnv, personalServices, username,
        setServerDebugData,
        currentSpace, setServerTestRes} = props;
    let ws = useRef(null);
    const [isDebug, setIsDebug] = useState(false);
    const [debugFileUrl, setDebugFileUrl] = useState(null);
    const [debugId, setDebugId] = useState(null);
    const [messageApi, contextHolder] = message.useMessage();
    const query = getQueryParams();

    // 服务端单步调试 基本传输信息
    const getStartDebugInfo = () => {
        return JSON.stringify({
            debugId: debugId, // 前端的debugId (uuid)；string；必填
            moduleId: currentSpace?.id, // 业务模块id；int；必填
            actionType: 1, // 动作类型 1-用例开启调试 2-用例结束调试；3-步骤开启调试 4-步骤结束调试；int；必填
            caseNodeId: +query?.caseNodeId, // 用例节点Id; int; 必填
            debugEnvId: currentEnv?.envId, // env的id；int；必填
            debugFileUrl: debugFileUrl,
            userServerList: personalServices?.configInfo,
            createUser: username
        });
    };

    const getEndDebugInfo = () => {
        return JSON.stringify({
            debugId: debugId, // 前端的debugId (uuid)；string；必填
            actionType: 2, // 动作类型 1-用例开启调试 2-用例结束调试；3-步骤开启调试 4-步骤结束调试；int；必填
            createUser: username
        });
    };

    // 初始化 WebSocket 连接 (caseNodeId 和 stepId 变化时)
    useEffect(() => {
        if (debugId !== null) {
            ws = new WebSocket(WS_DEBUG_BASICURL + '/core/api/debug/'
                + debugId + '/' + query?.caseNodeId);

            ws.onopen = () => {
                setServerTestRes({
                    actionType: 'case',
                    stepList: [],
                    status: 0,
                    debugId: debugId
                });
                console.log('WebSocket 连接');
                sendMessage(getStartDebugInfo());
            };

            ws.onmessage = (event) => {
                let data = JSON.parse(event.data);

                // 任务状态, 0-执行中 1-执行失败 2-执行成功 3-执行异常 4-主动取消
                if ([1, 3]?.includes(data?.status)) {
                    messageApi.error('执行失败：' + JSON.parse(data?.errorInfo)?.message);
                }
                if ([4]?.includes(data?.status)) {
                    messageApi.success('取消成功');
                }
                setServerTestRes({
                    actionType: 'case',
                    ...data
                });
                // 执行结束关闭连接
                if (data?.status && ![0]?.includes(data?.status) &&
                    ws?.current !== null && ws !== null) {
                    ws.close();
                    ws = null;
                    setIsDebug(false);
                }
            };

            ws.onerror = (event) => {
                messageApi.error('调试失败');
                setIsDebug(false);
                console.log('WebSocket 报错' + event);
            };
        }
        return () => {
            if (ws?.current !== null && ws !== null) {
                ws.close();
                ws = null;
                setIsDebug(false);
            }
            console.log('销毁');
        };
    }, [debugId]);

    // 尝试发送数据的函数，应该只在连接打开后调用
    // 0 (CONNECTING): 正在连接中
    // 1 (OPEN): 连接已建立，可以通信
    // 2 (CLOSING): 连接正在关闭
    // 3 (CLOSED): 连接已关闭或无法打开
    const sendMessage = (message) => {
        if (ws?.readyState === WebSocket.OPEN) {
            ws.send(message);
        }
    };

    return (
        <>
            {contextHolder}
            <div
                className={classnames(styles.runSettingBtn)}
                onClick={() => {
                    if (isDebug) {
                        // 结束调试
                        messageApi.info('调试取消中，请稍后');
                        sendMessage(getEndDebugInfo());
                    } else {
                        // 获取调试文档
                        post('/core/api/debug/case/file', {
                            caseNodeId: query?.caseNodeId,
                            debugEnvId: currentEnv?.envId,
                            moduleId: currentSpace?.id,
                            userServerList: personalServices?.configInfo
                        }).then(res => {
                            // 获取步骤详情
                            if (res?.debugFileUrl) {
                                fetch(res.debugFileUrl).then((response) => response.json()).then(data => {
                                setServerDebugData(data);
                                setDebugFileUrl(res?.debugFileUrl);
                                // 创建新的调试
                                let debug = v4().replace(/-/g, '');
                                setServerTestRes({
                                    actionType: 'case',
                                    stepList: [],
                                    status: 0,
                                    debugId: debug
                                });
                                setDebugId(debug);
                                setIsDebug(true);
                                }).catch(err => {
                                    messageApi.error('获取调试文档失败');
                                });
                            } else {
                                messageApi.error('获取调试文档URL失败');
                            }
                        }).catch(() => {
                            setIsDebug(false);
                            setServerTestRes(null);
                            setServerDebugData([]);
                        });
                    }
                }}
            >
                <CodeSandboxOutlined />
                <span style={{fontSize: '12px', marginLeft: 5}}>
                    {isDebug ? '结束调试' : '执行调试'}
                </span>
            </div>
        </>
    );
};

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    currentEnv: state.common.base.currentEnv,
    username: state.common.base.username,
    serverTestRes: state.common.case.serverTestRes,
    serverDebugData: state.common.case.serverDebugData,
    serverDebugId: state.common.case.serverDebugId,
    personalServices: state.common.case.personalServices,
}))(ServerCaseDebug);
