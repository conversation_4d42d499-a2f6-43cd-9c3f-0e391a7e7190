import { stringifyUrl } from 'query-string';
import { isEmpty } from 'lodash';
import electron from './electron';
import { PRECISION_BASICURL, TESTONE_BASICURL } from 'COMMON/config/baseUrl';
import store from '../middleware/store';
import { message } from 'antd';

const isDebug = false;

const handleFetchError = async (error, url) => {
    store.dispatch({
        type: 'common.base.throwNotification.setThrowNotification',
        params: {
            throwNotification: { ...error, url }
        }
    });
};

const convertPostOptions = (url, params, options) => {
    const state = store.getState();
    let defaultOptions = {
        method: 'POST',
        body: JSON.stringify(params),
        ...options,
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
            ...(options?.headers || {})
        }
    };
    if (options.headers && options.headers['Content-Encoding'] === 'gzip') {
        defaultOptions.body = params;
    }
    if (url.includes('/lazyone')) {
        defaultOptions.headers['Accept-Encoding'] = 'gzip';
    }
    if (url.includes('/lazymind')) {
        defaultOptions.headers['lazymind-version'] = state?.common?.base?.lazyMindVersion;
    }
    if (!url.includes(PRECISION_BASICURL) && !url.includes(TESTONE_BASICURL)) {
        defaultOptions = addQAMateHeaders(defaultOptions);
    }
    return defaultOptions;
};

const addQAMateHeaders = (options) => {
    const state = store.getState();
    options.headers['QAMate-U-Token'] = state?.common?.base?.token;
    if (state?.common?.base?.currentSpace?.id) {
        options.headers['QAMate-ModuleId'] = state?.common?.base?.currentSpace?.id;
    }
    return options;
};

const convertGetOptions = (url, options) => {
    const state = store.getState();

    let defaultOptions = {
        ...options,
        headers: {
            'QAMate-U-Token': state?.common?.base?.token,
            ...(options?.headers || {})
        }
    };

    if (url.includes('/lazymind')) {
        defaultOptions.headers['lazymind-version'] = state?.common?.base?.lazyMindVersion;
    }
    return defaultOptions;
};

// 精准请求 独立处理
const isPrecisionUrl = (url) => {
    return url.startsWith(PRECISION_BASICURL);
};

const responseError = (response, url, reject) => {
    // 输出错误信息
    handleFetchError(
        {
            code: response.status,
            msg: response.error
        },
        url
    );
    reject();
};

export function get(url, params, options = {}) {
    if (!url) {
        return Promise.reject('请输入请求地址');
    }
    return new Promise((resolve, reject) => {
        if (isDebug) {
            electron
                .send('network.request', {
                    path: url,
                    query: params,
                    ...convertGetOptions(url, options)
                })
                .then((data) => {
                    resolveData(resolve, reject, data, url);
                })
                .catch((err) => {
                    console.log(url, params, 'err');
                    reject(isEmpty(err) ? '请求失败' : err?.message ?? err);
                });
        } else {
            Promise.race([
                fetch(resolveUrl(stringifyUrl({ url: url, query: params })), { ...convertGetOptions(url, options) })
                    .then((response) => {
                        return response.text();
                    })
                    .then((text) => {
                        let data = JSON.parse(text ?? '{}');
                        console.log('data', url, data);
                        if (!data?.status) {
                            resolveData(resolve, reject, data, url);
                        } else {
                            responseError(data, url, reject);
                        }
                    })
                    .catch((err) => {
                        console.log('catch', url, err);
                        handleFetchError(err, url);
                        reject(err);
                    }),
                new Promise((resovle, reject) => {
                    setTimeout(() => {
                        reject({
                            code: 502,
                            message: '请求超时，请稍后再试！'
                        });
                    }, 20000);
                })
            ]).catch((err) => reject());
        }
    });
}

export function post(url, params, options = {}) {
    if (!url) {
        return Promise.reject('请输入请求地址');
    }
    return new Promise((resolve, reject) => {
        if (isDebug) {
            electron
                .send('network.request', {
                    path: url,
                    ...convertPostOptions(url, params, options)
                })
                .then((data) => {
                    resolveData(resolve, reject, data, url);
                })
                .catch((err) => {
                    console.log('error', url, params);
                    reject(isEmpty(err) ? '请求失败' : err?.message ?? err);
                });
        } else {
            Promise.race([
                fetch(resolveUrl(url), convertPostOptions(url, params, options))
                    .then((response) => response.json())
                    .then((data) => {
                        if (!data?.status) {
                            resolveData(resolve, reject, data, url);
                        } else {
                            responseError(data, url, reject);
                        }
                    })
                    .catch((err) => {
                        handleFetchError(err, url);
                        reject(err);
                    }),
                new Promise((resovle, reject) => {
                    setTimeout(() => {
                        reject('请求超时，请稍后再试！');
                    }, 60000);
                })
            ]).catch((err) => {
                reject(err);
            });
        }
    });
}

function resolveData(resolve, reject, data, url) {
    if (0 === data?.code || 0 === data?.errno || 0 === data?.status || data?.code === 200) {
        try {
            resolve(JSON.parse(data.data));
        } catch {
            resolve(data.data);
        }
    } else if (data?.code === 40001 && data?.redirectUrl) {
        message.warning({
            content: '登录态失效，即将跳转登录页',
            duration: 0
        });
        window.location.href = data?.redirectUrl; // 手动重定向
    } else if (data?.code === 11012) {
        // 登录过期
        message.warning({
            content: (
                <>
                    当前登录态失效或未登录，请尝试
                    <a style={{ textDecoration: 'underline' }} onClick={() => window.location.reload(true)}>
                        刷新
                    </a>
                    下页面
                </>
            ),
            duration: 0
        });
    } else if (data?.code === 1301701) {
        // diff 接口超时
        reject(data);
    } else {
        handleFetchError(data, url);
        reject(data);
    }
}

export const upload = (url, formData, options = {}) => {
    let uploadOptions = {
        method: 'post',
        headers: {
            ...options.headers
        },
        body: formData
    };
    uploadOptions = addQAMateHeaders(uploadOptions);
    return new Promise((resolve, reject) => {
        fetch(resolveUrl(url), uploadOptions)
            .then((response) => response.json())
            .then((data) => {
                resolveData(resolve, reject, data, url);
            })
            .catch(reject);
    });
};

function resolveUrl(url) {
    if (/^http(s)?/.test(url)) {
        return url;
    }
    return url[0] === '/' ? url : `/${url}`;
}
